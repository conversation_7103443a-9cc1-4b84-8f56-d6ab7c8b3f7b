{"type": "module", "scripts": {"dev": "node server.js --dev", "start": "node server.js", "build": "node build.js", "build:client": "vite build --ssrManifest", "build:server": "vite build --ssr", "build:full": "npm run build:client && npm run build:server && npm run build", "devinstall": "zx ../../devinstall.mjs -- node server.js --dev", "lint": "eslint . --ext .js,.jsx --fix"}, "dependencies": {"@sentry/react": "^9.24.0", "@stripe/firestore-stripe-payments": "^0.0.6", "ace-builds": "^1.41.0", "dotenv": "^16.4.7", "express": "^4.21.2", "firebase": "^11.6.0", "form-data": "^4.0.2", "history": "^5.3.0", "leetcode-query": "^2.0.0", "minipass": "^7.0.4", "monaco-editor": "^0.52.2", "multer": "^1.4.5-lts.2", "prismjs": "^1.30.0", "react": "^19.1.0", "react-ace": "^14.0.1", "react-dom": "^19.1.0", "react-feather": "^2.0.10", "react-markdown": "^10.1.0", "react-router-dom": "^6.20.0", "react-simple-code-editor": "^0.14.1", "recordrtc": "^5.6.2", "vike": "^0.4.228", "vike-react": "^0.6.1"}, "devDependencies": {"@vitejs/plugin-react": "^4.3.4", "postcss": "^8.4.31", "postcss-nesting": "^12.0.2", "postcss-preset-env": "^7.7.1", "tailwindcss": "^3.4.1", "vite": "^5.0.2", "vite-plugin-monaco-editor": "^1.1.0"}}
/** @type {import('tailwindcss').Config} */
export default {
  content: ["./client/index.html", "./client/**/*.{jsx,tsx}"],
  theme: {
    extend: {
      colors: {
        // Japanese-inspired color palette
        zen: {
          // <PERSON><PERSON> (Black Ink)
          ink: '#1A1A1A',
          // <PERSON><PERSON> (White)
          paper: '#F7F3E9',
          // <PERSON><PERSON><PERSON> (Deep Red)
          red: '#A02C2C',
          // <PERSON><PERSON> (Pale Blue)
          blue: '#7BA7BC',
          // <PERSON><PERSON> (Green)
          green: '#5A7247',
          // <PERSON><PERSON><PERSON> (Fallen Leaves)
          accent: '#D3A76D',
          // <PERSON> (Wisteria Purple)
          purple: '#8C6A93',
          // <PERSON><PERSON><PERSON> (<PERSON> Brown)
          brown: '#6A4C3B',
          // <PERSON><PERSON> (Crimson Red)
          highlight: '#A02C2C'
        }
      },
      fontFamily: {
        'zen': ['"Noto Serif JP"', 'serif'],
        'code': ['"Fira Code"', '"Fira Mono"', 'monospace']
      },
      backgroundImage: {
        'zen-pattern': "url('/assets/zen-pattern.svg')",
        'samurai-pattern': "url('/assets/samurai-pattern.svg')"
      },
      boxShadow: {
        'zen': '0 4px 8px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1)',
        'zen-hover': '0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05)'
      },
      borderRadius: {
        'zen': '0.25rem'
      }
    },
  },
  plugins: [],
};

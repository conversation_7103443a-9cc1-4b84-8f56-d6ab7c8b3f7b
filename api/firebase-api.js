import { collection, addDoc, serverTimestamp, query, where, getDocs, orderBy, limit as firestoreLimit, startAfter, getCountFromServer, limit } from 'firebase/firestore'
import { db } from '../firebase'

/**
 * Saves user feedback to Firebase Firestore in a separate feedback collection
 * @param {Object} parsedFeedback - The feedback object containing score, feedback text, etc.
 * @param {string} userId - The ID of the user who provided the feedback
 * @param {Object} questionInfo - Information about the question (id, title)
 * @returns {Promise} - Promise that resolves when the feedback is saved
 */
export async function saveFeedback(parsedFeedback, userId, questionInfo) {

    if (!userId) {
        console.error('Cannot save feedback: No user ID provided')
        return
    }

    try {
        // Create a reference to the feedback collection
        const feedbackCollectionRef = collection(db, 'feedback')

        // Create a new feedback document
        await addDoc(feedbackCollectionRef, {
            userId: userId,
            score: parsedFeedback.score,
            scoreJustification: parsedFeedback.score_justification,
            feedback: parsedFeedback.feedback,
            subScores: parsedFeedback.sub_scores || {},
            questionId: questionInfo?.id || 'unknown',
            questionTitle: questionInfo?.title || 'unknown',
            difficulty: questionInfo?.difficulty || 'unknown',
            timestamp: serverTimestamp(),
            createdAt: serverTimestamp()
        })

    } catch (error) {
        console.error('Error saving feedback to Firebase:', error)
    }
}

/**
 * Retrieves paginated feedback for a specific user with search, sort, and filter capabilities
 * @param {string} userId - The ID of the user to get feedback for
 * @param {Object} options - Query options
 * @param {number} options.page - Current page number (1-based)
 * @param {number} options.pageSize - Number of items per page
 * @param {string} options.difficulty - Filter by difficulty level
 * @param {string} options.sortBy - Field to sort by ('timestamp', 'score', etc.)
 * @param {string} options.sortOrder - Sort order ('asc' or 'desc')
 * @returns {Promise<{feedback: Array, total: number, totalPages: number}>} - Promise that resolves to feedback array and pagination info
 */
export async function getUserFeedback(userId, options = {}) {
    if (!userId) {
        console.error('Cannot get feedback: No user ID provided')
        return { feedback: [], total: 0, totalPages: 0 }
    }

    const {
        page = 1,
        pageSize = 10,
        difficulty = '',
        sortBy = 'timestamp',
        sortOrder = 'desc'
    } = options

    try {
        console.log('Fetching feedback with options:', { userId, page, pageSize, difficulty, sortBy, sortOrder })

        // Create a reference to the feedback collection
        const feedbackCollectionRef = collection(db, 'feedback')

        // Build the base query
        let constraints = [where('userId', '==', userId)]

        // Add difficulty filter if specified
        if (difficulty) {
            constraints.push(where('difficulty', '==', difficulty))
        }

        // Get total count for pagination
        const countQuery = query(feedbackCollectionRef, ...constraints)
        const totalSnapshot = await getCountFromServer(countQuery)
        const total = totalSnapshot.data().count
        const totalPages = Math.ceil(total / pageSize)

        console.log('Total items:', total, 'Total pages:', totalPages)

        // If we're not on the first page, we need to get the last document from the previous page
        let lastDoc = null
        if (page > 1) {
            // Get the last document from the previous page
            const prevPageQuery = query(
                feedbackCollectionRef,
                ...constraints,
                orderBy(sortBy, sortOrder),
                firestoreLimit((page - 1) * pageSize)
            )
            const prevPageSnapshot = await getDocs(prevPageQuery)
            lastDoc = prevPageSnapshot.docs[prevPageSnapshot.docs.length - 1]
        }

        // Create the main query
        let q = query(
            feedbackCollectionRef,
            ...constraints,
            orderBy(sortBy, sortOrder),
            firestoreLimit(pageSize)
        )

        // If we have a last document, start after it
        if (lastDoc) {
            q = query(q, startAfter(lastDoc))
        }

        // Execute the query
        const querySnapshot = await getDocs(q)
        console.log('Query returned', querySnapshot.size, 'documents')

        // Map the query results to an array of feedback objects
        const feedbackList = []
        querySnapshot.forEach((doc) => {
            const data = doc.data()

            // Process the feedback data to handle both old and new formats
            const processedFeedback = {
                id: doc.id,
                ...data,
                feedback: processFeedbackData(data.feedback)
            }

            feedbackList.push(processedFeedback)
        })

        console.log('Processed feedback list length:', feedbackList.length)

        return {
            feedback: feedbackList,
            total,
            totalPages
        }
    } catch (error) {
        console.error('Error retrieving user feedback from Firebase:', error)
        console.error('Error details:', {
            code: error.code,
            message: error.message,
            stack: error.stack
        })
        return { feedback: [], total: 0, totalPages: 0 }
    }
}

/**
 * Process feedback data to ensure consistent structure for rendering
 * Handles both string and object formats
 * @param {any} feedback - The feedback data from Firestore
 * @returns {Object} - Processed feedback object
 */
function processFeedbackData(feedback) {
    // If feedback is a string, try to parse it
    if (typeof feedback === 'string') {
        try {
            return JSON.parse(feedback)
        } catch (e) {
            console.error('Failed to parse feedback string:', e)
            return { text: feedback } // Fallback to treating it as plain text
        }
    }

    // If feedback is already an object with category structure
    if (feedback && typeof feedback === 'object') {
        // Check if it has the expected structure with categories and points
        const hasExpectedStructure = Object.values(feedback).some(
            value => value && typeof value === 'object' && Array.isArray(value.points)
        )

        if (hasExpectedStructure) {
            return feedback // Already in the right format
        }
    }

    // Default fallback
    return feedback || {}
}

/**
 * Gets percentile data for multiple questions and scores in a single batch
 * @param {Array<{questionId: string, score: number}>} items - Array of question IDs and scores
 * @returns {Promise<Object>} - Promise that resolves to an object mapping question IDs to percentile data
 */
export async function getBatchQuestionPercentiles(items) {
    if (!items || !items.length) {
        return {};
    }

    try {
        // Create a reference to the feedback collection
        const feedbackCollectionRef = collection(db, 'feedback');

        // Get unique question IDs
        const uniqueQuestionIds = [...new Set(items.map(item => item.questionId))];

        // Create a map to store percentile data for each question
        const questionPercentiles = {};

        // Fetch all percentile data for the unique questions in parallel
        const percentilePromises = uniqueQuestionIds.map(async (questionId) => {
            const q = query(
                feedbackCollectionRef,
                where('questionId', '==', questionId)
            );
            const querySnapshot = await getDocs(q);
            const percentileDoc = querySnapshot.docs[0];
            if (percentileDoc) {
                const data = percentileDoc.data();
                questionPercentiles[questionId] = {
                    percentile: data.percentile || 0
                };
            }
        });

        await Promise.all(percentilePromises);

        // Map scores to percentiles using the pre-calculated data
        const results = {};
        items.forEach(({ questionId, score }) => {
            const percentileData = questionPercentiles[questionId];
            results[questionId] = percentileData || { percentile: 0 };
        });

        return results;
    } catch (error) {
        console.error('Error getting batch question percentiles:', error);
        return {};
    }
}
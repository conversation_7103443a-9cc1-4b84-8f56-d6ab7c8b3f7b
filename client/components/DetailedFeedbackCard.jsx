import React from 'react'
import ReactMarkdown from 'react-markdown'

/**
 * Component to display detailed feedback with categories and points
 * Handles both old and new feedback formats
 */
export default function DetailedFeedbackCard({ feedback, formatDate }) {
  // Ensure we have a valid feedback object
  if (!feedback) return null

  // Helper function to render feedback category with points
  const renderFeedbackCategory = (category, data) => {
    if (!data || !data.points || !Array.isArray(data.points) || data.points.length === 0) {
      return null
    }

    // Format category name for display
    const categoryLabel = category
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ')

    return (
      <div key={category} className="mb-4 border-t border-zen-accent/20 pt-3">
        <div className="flex justify-between items-center mb-2">
          <h5 className="font-zen text-sm text-zen-ink dark:text-zen-paper">{categoryLabel}</h5>
          {data.score !== undefined && (
            <span className="text-zen-highlight dark:text-zen-accent font-bold text-sm">
              {data.score}
            </span>
          )}
        </div>
        <ul className="list-disc pl-5 space-y-1">
          {data.points.map((point, idx) => (
            <li key={idx} className="text-zen-ink dark:text-zen-paper text-sm leading-relaxed">
              {point}
            </li>
          ))}
        </ul>
      </div>
    )
  }

  // Determine if feedback has the new structured format with categories and points
  const hasStructuredFeedback = 
    feedback.feedback && 
    typeof feedback.feedback === 'object' &&
    Object.values(feedback.feedback).some(
      value => value && typeof value === 'object' && Array.isArray(value.points)
    )

  return (
    <div className="mb-6 p-4 border border-zen-accent/20 rounded-zen">
      <div className="flex flex-wrap justify-between items-center mb-3 pb-2 border-b border-zen-accent/10">
        <h4 className="font-zen text-zen-ink dark:text-zen-paper">
          {feedback.questionTitle || 'Unknown Question'}
        </h4>
        <div className="flex items-center gap-2">
          <span className="text-sm font-zen text-zen-ink/70 dark:text-zen-paper/70">
            {formatDate(feedback.timestamp)}
          </span>
          <span className="text-zen-highlight dark:text-zen-accent font-bold">
            {feedback.score}
          </span>
          <span className="text-zen-ink/50 dark:text-zen-paper/50 text-sm">/ 100</span>
        </div>
      </div>
      
      {/* Assessment/Justification Section */}
      <div className="mb-3">
        <h5 className="font-zen text-sm text-zen-ink dark:text-zen-paper mb-1">Assessment:</h5>
        <div className="text-zen-ink dark:text-zen-paper whitespace-pre-wrap font-zen text-sm leading-relaxed break-words prose dark:prose-invert max-w-none">
          <ReactMarkdown>{feedback.scoreJustification}</ReactMarkdown>
        </div>
      </div>
      
      {/* Sub-scores Section */}
      {feedback.subScores && Object.keys(feedback.subScores).length > 0 && (
        <div className="mb-3">
          <h5 className="font-zen text-sm text-zen-ink dark:text-zen-paper mb-1">Detailed Scores:</h5>
          <div className="grid grid-cols-2 gap-2">
            {Object.entries(feedback.subScores).map(([key, score]) => {
              const label = key.split('_').map(word => 
                word.charAt(0).toUpperCase() + word.slice(1)
              ).join(' ');
              return (
                <div key={key} className="flex justify-between items-center text-sm">
                  <span className="text-zen-ink/70 dark:text-zen-paper/70">{label}:</span>
                  <span className="text-zen-highlight dark:text-zen-accent font-bold pr-20">{score}</span>
                </div>
              );
            })}
          </div>
        </div>
      )}
      
      {/* Detailed Feedback with Categories and Points */}
      {hasStructuredFeedback && (
        <div className="mb-3">
          <h5 className="font-zen text-sm text-zen-ink dark:text-zen-paper mb-2">Detailed Feedback:</h5>
          <div className="space-y-2">
            {Object.entries(feedback.feedback).map(([category, data]) => 
              renderFeedbackCategory(category, data)
            )}
          </div>
        </div>
      )}
      
      {/* Legacy Feedback Display (if not structured) */}
      {!hasStructuredFeedback && typeof feedback.feedback === 'string' && (
        <div>
          <h5 className="font-zen text-sm text-zen-ink dark:text-zen-paper mb-1">Improvement Path:</h5>
          <div className="text-zen-ink dark:text-zen-paper whitespace-pre-wrap font-zen text-sm leading-relaxed break-words prose dark:prose-invert max-w-none">
            <ReactMarkdown>{feedback.feedback}</ReactMarkdown>
          </div>
        </div>
      )}
    </div>
  )
}

import { useRef, useState, useEffect } from 'react'
import { saveFeedback } from '../../api/firebase-api'
import { getScoreEmoji, subScoreConfig } from '../utils/styling'
import { collection, query, where, getDocs } from 'firebase/firestore'
import { db } from '../../firebase'

// Add this function at the top level
const getScoreBasedColors = (score, max) => {
  const percentage = (score / max) * 100
  if (percentage >= 80) {
    return {
      text: 'text-green-600 dark:text-green-400',
      bg: 'bg-green-500'
    }
  } else if (percentage >= 60) {
    return {
      text: 'text-blue-600 dark:text-blue-400',
      bg: 'bg-blue-500'
    }
  } else if (percentage >= 40) {
    return {
      text: 'text-yellow-600 dark:text-yellow-400',
      bg: 'bg-yellow-500'
    }
  } else {
    return {
      text: 'text-red-600 dark:text-red-400',
      bg: 'bg-red-500'
    }
  }
}

/**
 * Component to display interview feedback
 */
export default function FeedbackDisplay({
  feedback,
  onReset,
  recordingError,
  user,
  currentQuestion,
  handleTabChange,
  trialMode = false
}) {
  let parsedFeedback = null
  const feedbackSavedRef = useRef(false)
  const [percentileData, setPercentileData] = useState(null)

  // Utility function to safely convert any value to a string for ReactMarkdown
  const ensureString = (value) => {
    if (value === null || value === undefined) {
      return ''
    }
    if (typeof value === 'string') {
      return value
    }
    try {
      return JSON.stringify(value, null, 2)
    } catch (e) {
      return String(value)
    }
  }

  try {
    // Get content from the response - could be a string or already an object
    const rawContent = feedback?.choices?.[0]?.message?.content

    // Parse the content if it's a string, otherwise use it directly
    if (typeof rawContent === 'string') {
      try {
        parsedFeedback = JSON.parse(rawContent)
      } catch (parseError) {
        // If parsing fails, create a basic structure
        parsedFeedback = {
          score: 0,
          score_justification: "Failed to parse feedback: " + parseError.message,
          feedback: {},
          sub_scores: {}
        }
      }
    } else if (typeof rawContent === 'object') {
      // Content is already an object
      parsedFeedback = rawContent
    } else {
      parsedFeedback = {
        score: 0,
        score_justification: "Unexpected feedback format",
        feedback: {},
        sub_scores: {}
      }
    }

    // Ensure score_justification is a string
    if (parsedFeedback.score_justification && typeof parsedFeedback.score_justification !== 'string') {
      parsedFeedback.score_justification = JSON.stringify(parsedFeedback.score_justification)
    }

    // Ensure sub_scores exist
    if (!parsedFeedback.sub_scores) {
      parsedFeedback.sub_scores = {}

      // Try to extract scores from feedback object if it exists
      if (parsedFeedback.feedback) {
        Object.entries(parsedFeedback.feedback).forEach(([key, value]) => {
          if (value && typeof value === 'object' && 'score' in value) {
            parsedFeedback.sub_scores[key] = value.score
          }
        })
      }
    }

    // Save feedback to Firebase if we have valid data (skip in trial mode)
    if (parsedFeedback && user && !feedbackSavedRef.current && !trialMode) {
      const questionInfo = currentQuestion || {
        id: 'unknown',
        title: 'Unknown Question',
        difficulty: 'unknown'
      }
      saveFeedback(parsedFeedback, user.uid, questionInfo)
      feedbackSavedRef.current = true
    }
  } catch (e) {
    // Handle any errors in processing feedback
  }

  // Add useEffect to calculate percentile data (skip in trial mode)
  useEffect(() => {
    const calculatePercentile = async () => {
      if (parsedFeedback?.score && currentQuestion?.id && !trialMode) {
        try {
          // Create a reference to the feedback collection
          const feedbackCollectionRef = collection(db, 'feedback');

          // Query all feedback for this question
          const q = query(
            feedbackCollectionRef,
            where('questionId', '==', currentQuestion.id)
          );

          const querySnapshot = await getDocs(q);
          const scores = [];

          // Collect all scores
          querySnapshot.forEach((doc) => {
            const data = doc.data();
            if (data.score !== undefined) {
              scores.push(data.score);
            }
          });

          // Calculate percentile
          if (scores.length > 0) {
            // Sort scores in ascending order
            scores.sort((a, b) => a - b);

            // Find how many scores are less than the current score
            const lessThanCount = scores.filter(score => score < parsedFeedback.score).length;

            // Calculate percentile
            const percentile = (lessThanCount / scores.length) * 100;

            setPercentileData({
              percentile,
              totalAttempts: scores.length
            });
          } else {
            setPercentileData({ percentile: 0, totalAttempts: 0 });
          }
        } catch (error) {
          console.error('Error calculating percentile:', error);
          setPercentileData({ percentile: 0, totalAttempts: 0 });
        }
      }
    };

    calculatePercentile();
  }, [parsedFeedback?.score, currentQuestion?.id]);

  // If no feedback yet, show loading state
  if (!parsedFeedback) {
    return (
      <div className={`min-h-screen flex items-center justify-center p-4 md:p-8 ${trialMode ? 'bg-zen-pattern dark:bg-zen-pattern' : 'bg-zen-pattern dark:bg-samurai-pattern'}`}>
        <div className="bg-zen-paper dark:bg-zen-ink p-8 rounded-zen shadow-zen">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-zen-accent mb-4"></div>
          <p className="text-zen-ink dark:text-zen-paper font-zen text-lg">Loading feedback...</p>
        </div>
      </div>
    )
  }

  return (
    <div className={`min-h-screen flex items-center justify-center p-4 md:p-8 ${trialMode ? 'bg-zen-pattern dark:bg-zen-pattern' : 'bg-zen-pattern dark:bg-samurai-pattern'}`}>
      <div className="bg-zen-paper dark:bg-zen-ink p-4 md:p-8 rounded-zen shadow-zen w-full max-w-4xl max-h-[90vh] overflow-y-auto border border-zen-accent/20">
        <div className="flex flex-col md:flex-row md:items-center mb-6 border-b border-zen-accent/30 pb-4">
          <h2 className="text-2xl md:text-3xl font-zen text-zen-ink dark:text-zen-paper">
            {trialMode ? "Your Interview Results" : "Your Path of Mastery"}
          </h2>
          <div className="mt-2 md:mt-0 md:ml-auto text-zen-accent">
            {parsedFeedback?.score && (
              <div className="flex flex-col items-end">
                <span className="text-2xl font-bold">
                  Overall Score: {parsedFeedback.score}/100
                </span>
                {percentileData && percentileData.totalAttempts > 0 && (
                  <span className="text-sm text-zen-ink dark:text-zen-paper mt-1">
                    You scored better than {Math.round(percentileData.percentile)}% of candidates
                  </span>
                )}
              </div>
            )}
          </div>
        </div>

        {recordingError && (
          <div className="bg-zen-red/10 border border-zen-red/30 rounded-zen p-4 mb-6">
            <p className="text-zen-red font-zen text-sm md:text-base break-words">{recordingError}</p>
          </div>
        )}

        {parsedFeedback ? (
          <>
            {/* Score Cards Section */}
            {parsedFeedback.sub_scores && Object.keys(parsedFeedback.sub_scores).length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                {Object.entries(parsedFeedback.sub_scores).map(([key, score]) => {
                  const config = subScoreConfig[key]
                  if (!config) return null

                  return (
                    <div key={key} className="bg-zen-paper/50 dark:bg-zen-ink/50 p-4 rounded-zen border-l-4 border-zen-accent">
                      <div className="flex justify-between items-center mb-2">
                        <div className="flex items-center gap-2">
                          <span className="text-xl">{getScoreEmoji(score, config.max)}</span>
                          <h3 className="font-zen text-lg text-zen-ink dark:text-zen-paper">{config.label}</h3>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className={`text-2xl font-bold ${getScoreBasedColors(score, config.max).text}`}>
                            {score}
                          </span>
                          <span className="text-gray-600 dark:text-gray-400">/ {config.max}</span>
                        </div>
                      </div>
                      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div
                          className={`h-2 rounded-full ${getScoreBasedColors(score, config.max).bg}`}
                          style={{ width: `${(score / config.max) * 100}%` }}
                        />
                      </div>
                    </div>
                  )
                })}
              </div>
            ) : (
              <div className="bg-zen-paper/50 dark:bg-zen-ink/50 p-4 rounded-zen border-l-4 border-zen-accent mb-8">
                <p className="text-zen-ink/70 dark:text-zen-paper/70 font-zen">
                  Detailed scoring breakdown not available.
                </p>
              </div>
            )}

            {/* Assessment Section */}
            <div className="mb-8 bg-zen-paper/50 dark:bg-zen-ink/50 p-6 rounded-zen border border-zen-accent/30">
              <h3 className="font-zen text-xl text-zen-ink dark:text-zen-paper mb-3">
                {trialMode ? "Performance Assessment" : "Sensei's Assessment"}
              </h3>
              <div className="text-zen-ink dark:text-zen-paper whitespace-pre-wrap font-zen leading-relaxed prose dark:prose-invert max-w-none">
                {parsedFeedback.score_justification ? (
                  <div className="text-zen-ink dark:text-zen-paper">
                    {ensureString(parsedFeedback.score_justification)}
                  </div>
                ) : (
                  <p className="text-zen-ink/70 dark:text-zen-paper/70">No assessment available.</p>
                )}
              </div>
            </div>

            {/* Feedback Section - Handle different feedback formats */}
            <div className="mb-8 bg-zen-paper/50 dark:bg-zen-ink/50 p-6 rounded-zen border border-zen-accent/30">
              <h3 className="font-zen text-xl text-zen-ink dark:text-zen-paper mb-3">
                {trialMode ? "Areas for Improvement" : "Path to Improvement"}
              </h3>
              <div className="text-zen-ink dark:text-zen-paper whitespace-pre-wrap font-zen leading-relaxed prose dark:prose-invert max-w-none">
                {typeof parsedFeedback.feedback === 'string' ? (
                  <div className="text-zen-ink dark:text-zen-paper">
                    {ensureString(parsedFeedback.feedback)}
                  </div>
                ) : parsedFeedback.feedback && typeof parsedFeedback.feedback === 'object' ? (
                  // Handle structured feedback object
                  <div className="space-y-6">
                    {Object.entries(parsedFeedback.feedback).map(([category, data]) => {
                      const config = subScoreConfig[category]
                      if (!config || !data) return null

                      return (
                        <div key={category} className="border-t border-zen-accent/20 pt-4">
                          <h4 className="font-zen text-lg text-zen-ink dark:text-zen-paper mb-2">{config.label}</h4>
                          {data.points && Array.isArray(data.points) ? (
                            <ul className="list-disc pl-5 space-y-2">
                              {data.points.map((point, idx) => (
                                <li key={idx}>{point}</li>
                              ))}
                            </ul>
                          ) : (
                            <p className="text-zen-ink/70 dark:text-zen-paper/70">No details available.</p>
                          )}
                        </div>
                      )
                    })}
                  </div>
                ) : (
                  <p className="text-zen-ink/70 dark:text-zen-paper/70">No feedback available.</p>
                )}
              </div>
            </div>
          </>
        ) : (
          <div className="text-center p-8">
            <p className="text-zen-red font-zen text-lg">No feedback available.</p>
            <p className="text-zen-ink/70 dark:text-zen-paper/70 mt-2">The feedback structure is incomplete or invalid.</p>
          </div>
        )}

        {!trialMode && (
          <div className="flex flex-col md:flex-row gap-4 mt-6">
            <button
              onClick={onReset}
              className="px-6 py-3 bg-zen-ink dark:bg-zen-accent text-zen-paper font-zen rounded-zen hover:bg-zen-highlight dark:hover:bg-zen-purple transition-all transform hover:-translate-y-1 shadow-zen-hover"
            >
              Begin a New Journey
            </button>

            <button
              onClick={() => {
                onReset()
                setTimeout(() => {
                  handleTabChange('profile')
                }, 100)
              }}
              className="px-6 py-3 bg-zen-paper dark:bg-zen-ink text-zen-ink dark:text-zen-paper border border-zen-accent font-zen rounded-zen hover:bg-zen-accent/10 dark:hover:bg-zen-accent/20 transition-all transform hover:-translate-y-1 shadow-zen-hover"
            >
              View All Feedback
            </button>
          </div>
        )}
      </div>
    </div>
  )
}

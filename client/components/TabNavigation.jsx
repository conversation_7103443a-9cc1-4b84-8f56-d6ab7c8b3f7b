export default function TabNavigation({ activeTab, onTabChange }) {
  return (
    <div className="flex gap-2">
      <button
        onClick={() => onTabChange('interview')}
        className={`px-4 py-2 font-zen text-base transition-all ${
          activeTab === 'interview'
            ? 'text-zen-accent border-b-2 border-zen-accent'
            : 'text-zen-ink/70 dark:text-zen-paper/70 hover:text-zen-accent'
        }`}
      >
        Interview
      </button>
      <button
        onClick={() => onTabChange('profile')}
        className={`px-4 py-2 font-zen text-base transition-all ${
          activeTab === 'profile'
            ? 'text-zen-accent border-b-2 border-zen-accent'
            : 'text-zen-ink/70 dark:text-zen-paper/70 hover:text-zen-accent'
        }`}
      >
        My Profile
      </button>
    </div>
  )
}

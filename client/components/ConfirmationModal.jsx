import React from 'react'

export default function ConfirmationModal({ isOpen, onConfirm, onCancel, title, message }) {
  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-zen-ink/50 dark:bg-zen-ink/70 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-zen-paper dark:bg-zen-ink p-6 rounded-zen shadow-zen border border-zen-accent/20 w-full max-w-md transform transition-all">
        <h3 className="text-xl font-zen text-zen-ink dark:text-zen-paper mb-3 border-b border-zen-accent/20 pb-2">
          {title || "Confirm Action"}
        </h3>
        <p className="text-zen-ink dark:text-zen-paper font-zen mb-6">
          {message || "Are you sure you want to proceed?"}
        </p>
        <div className="flex justify-end gap-4">
          <button
            onClick={onCancel}
            className="px-4 py-2 bg-zen-paper dark:bg-zen-ink text-zen-ink dark:text-zen-paper border border-zen-accent font-zen rounded-zen hover:bg-zen-accent/10 dark:hover:bg-zen-accent/20 transition-all"
          >
            Continue Training
          </button>
          <button
            onClick={onConfirm}
            className="px-4 py-2 bg-zen-red text-zen-paper font-zen rounded-zen hover:bg-zen-red/90 transition-all"
          >
            Abandon Path
          </button>
        </div>
      </div>
    </div>
  )
}

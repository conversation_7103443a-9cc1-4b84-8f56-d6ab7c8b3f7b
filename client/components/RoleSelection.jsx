import React from 'react';

const roles = [
  {
    id: 'software_engineer',
    title: 'Software Engineer',
    description: 'Master algorithms, data structures, and system design',
    enabled: true,
    emoji: '👨‍💻'
  },
  {
    id: 'frontend_engineer',
    title: 'Frontend Engineer',
    description: 'Specialize in UI/UX, React, and modern web technologies',
    enabled: false,
    emoji: '🎨'
  },
  {
    id: 'backend_engineer',
    title: 'Backend Engineer',
    description: 'Focus on server-side development, databases, and APIs',
    enabled: false,
    emoji: '⚙️'
  },
  {
    id: 'product_manager',
    title: 'Product Manager',
    description: 'Lead product strategy, user research, and feature prioritization',
    enabled: false,
    emoji: '📊'
  },
  {
    id: 'data_scientist',
    title: 'Data Scientist',
    description: 'Work with machine learning, statistics, and data analysis',
    enabled: false,
    emoji: '📈'
  }
];

export default function RoleSelection({ onSelect }) {
  return (
    <div className="h-screen overflow-y-auto bg-zen-pattern dark:bg-samurai-pattern">
      <div className="flex flex-col items-center justify-start min-h-full p-8">
        <div className="max-w-3xl w-full space-y-8 py-8">
          <div className="text-center mb-16">
            <h1 className="text-5xl font-zen text-zen-accent dark:text-zen-purple mb-6 bg-gradient-to-r from-zen-accent to-zen-purple bg-clip-text text-transparent">
              Choose Your Path 🎯
            </h1>
            <p className="text-zen-ink/70 dark:text-zen-paper/70 font-zen text-xl">
              Select your role to begin your training journey ✨
            </p>
          </div>
          
          <div className="grid gap-8">
            {roles.map((role) => (
              <button
                key={role.id}
                onClick={() => role.enabled && onSelect(role.id)}
                className={`group p-8 rounded-zen shadow-zen border transition-all duration-300 ${
                  role.enabled
                    ? 'bg-zen-paper/90 dark:bg-zen-ink/90 border-zen-accent/20 hover:border-zen-accent hover:shadow-lg hover:shadow-zen-accent/20 cursor-pointer hover:scale-[1.02] hover:bg-zen-paper dark:hover:bg-zen-ink'
                    : 'bg-zen-paper/50 dark:bg-zen-ink/50 border-zen-accent/10 cursor-not-allowed opacity-50'
                }`}
              >
                <div className="flex justify-between items-center">
                  <div className="flex items-center space-x-6">
                    <span className="text-4xl transform group-hover:scale-110 transition-transform">
                      {role.emoji}
                    </span>
                    <div>
                      <h3 className={`text-2xl font-zen mb-3 ${
                        role.enabled 
                          ? 'text-zen-ink dark:text-zen-paper group-hover:text-zen-accent dark:group-hover:text-zen-purple'
                          : 'text-zen-ink/70 dark:text-zen-paper/70'
                      }`}>
                        {role.title}
                      </h3>
                      <p className="text-zen-ink/70 dark:text-zen-paper/70 font-zen text-lg">{role.description}</p>
                    </div>
                  </div>
                  {!role.enabled && (
                    <span className="text-sm bg-zen-accent/10 text-zen-accent px-4 py-2 rounded-full font-medium">
                      Coming Soon 🚀
                    </span>
                  )}
                  {role.enabled && (
                    <span className="text-sm bg-zen-accent/10 text-zen-accent px-4 py-2 rounded-full font-medium opacity-0 group-hover:opacity-100 transition-opacity">
                      Select ⚡
                    </span>
                  )}
                </div>
              </button>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
} 
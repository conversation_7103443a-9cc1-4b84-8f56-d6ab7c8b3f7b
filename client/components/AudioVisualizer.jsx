import { useEffect, useRef } from 'react'

export default function AudioVisualizer({ stream }) {
  const canvasRef = useRef(null)

  useEffect(() => {
    if (!stream) return
    const canvas = canvasRef.current
    const ctx = canvas.getContext('2d')
    const audioCtx = new (window.AudioContext || window.webkitAudioContext)()
    const source = audioCtx.createMediaStreamSource(stream)
    const analyser = audioCtx.createAnalyser()
    analyser.fftSize = 256
    const dataArray = new Uint8Array(analyser.frequencyBinCount)
    source.connect(analyser)

    // Get CSS variables for colors
    const computedStyle = getComputedStyle(document.documentElement)
    const isDarkMode = document.documentElement.classList.contains('dark-theme')

    // Use theme colors
    const primaryColor = isDarkMode
      ? computedStyle.getPropertyValue('--color-dark-accent').trim()
      : computedStyle.getPropertyValue('--color-accent').trim()

    const secondaryColor = isDarkMode
      ? computedStyle.getPropertyValue('--color-highlight').trim()
      : computedStyle.getPropertyValue('--color-highlight').trim()

    function draw() {
      requestAnimationFrame(draw)
      analyser.getByteFrequencyData(dataArray)

      // Clear with transparent background
      ctx.clearRect(0, 0, canvas.width, canvas.height)

      const cx = canvas.width / 2
      const cy = canvas.height / 2
      const baseRadius = 60

      // Draw circular base (like a zen circle)
      ctx.beginPath()
      ctx.arc(cx, cy, baseRadius, 0, Math.PI * 2)
      ctx.strokeStyle = primaryColor
      ctx.lineWidth = 1
      ctx.stroke()

      // Draw audio visualization lines (like brush strokes)
      for (let i = 0; i < dataArray.length; i += 2) { // Skip every other value for cleaner look
        const v = dataArray[i]

        // Calculate line properties
        const barLength = v * 0.4
        const angle = (i / dataArray.length) * Math.PI * 2

        // Starting point (on the circle)
        const x1 = cx + baseRadius * Math.cos(angle)
        const y1 = cy + baseRadius * Math.sin(angle)

        // Ending point (extending outward)
        const x2 = cx + (baseRadius + barLength) * Math.cos(angle)
        const y2 = cy + (baseRadius + barLength) * Math.sin(angle)

        // Draw the line with gradient
        const gradient = ctx.createLinearGradient(x1, y1, x2, y2)
        gradient.addColorStop(0, primaryColor)
        gradient.addColorStop(1, secondaryColor)

        ctx.beginPath()
        ctx.moveTo(x1, y1)
        ctx.lineTo(x2, y2)
        ctx.strokeStyle = gradient
        ctx.lineWidth = 2
        ctx.lineCap = 'round' // Rounded ends for brush stroke effect
        ctx.stroke()
      }
    }

    draw()
    return () => audioCtx.close()
  }, [stream])

  return <canvas ref={canvasRef} width={280} height={180} className="w-full h-auto rounded-zen max-w-full" />
}

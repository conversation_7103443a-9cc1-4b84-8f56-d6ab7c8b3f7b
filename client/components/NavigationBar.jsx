import { signOut } from 'firebase/auth'
import { auth } from '../../firebase'
import TabNavigation from './TabNavigation'
import { formatTimeLeft } from '../utils/styling'
import logo from '/assets/samurai-logo.svg'

/**
 * Navigation bar component for the application
 */
export default function NavigationBar({ 
  activeTab, 
  onTabChange, 
  isSessionActive, 
  timeLeft, 
  userCredits, 
  onCreditModalOpen 
}) {
  /**
   * Handle user logout
   */
  async function handleLogout() {
    try {
      await signOut(auth)
      window.location.reload()
    } catch (error) {
      console.error('Logout failed', error)
    }
  }

  return (
    <nav className="absolute top-0 left-0 right-0 h-16 flex items-center bg-zen-paper dark:bg-zen-ink shadow-zen">
      <div className="flex items-center gap-2 md:gap-4 w-full mx-3 md:mx-6 py-3 border-b border-solid border-zen-accent/30">
        <img className="w-6 h-6 md:w-8 md:h-8 transition-transform hover:rotate-12" src={logo} alt="logo" />
        <h1 className="font-zen text-base md:text-xl tracking-wider text-zen-ink dark:text-zen-paper truncate">Samurai Code Dojo</h1>

        {/* Tab Navigation */}
        <div className="ml-4 md:ml-8 flex">
          <TabNavigation
            activeTab={activeTab}
            onTabChange={onTabChange}
          />
        </div>

        {isSessionActive && (
          <div className="ml-auto font-zen text-zen-ink dark:text-zen-paper">
            <span className="mr-1 md:mr-2">⏱</span> {formatTimeLeft(timeLeft)}
          </div>
        )}
        
        {!isSessionActive && (
          <div className="ml-auto flex items-center gap-4">
            <div className="flex items-center gap-3 bg-zen-paper/50 dark:bg-zen-ink/50 px-4 py-2 rounded-zen border border-zen-accent/20">
              <span className="font-zen text-zen-ink dark:text-zen-paper flex items-center gap-2">
                <span className="text-zen-accent">⚡</span>
                <span className="font-medium">{userCredits}</span>
                <span className="text-zen-ink/70 dark:text-zen-paper/70 text-sm">credits</span>
              </span>
              <button
                onClick={onCreditModalOpen}
                className="bg-zen-accent hover:bg-zen-highlight text-zen-paper px-4 py-1.5 rounded-zen transition-all transform hover:-translate-y-0.5 shadow-zen-hover font-zen text-sm"
              >
                Get More
              </button>
            </div>
            <button
              onClick={handleLogout}
              className="bg-zen-ink dark:bg-zen-purple text-zen-paper px-3 py-1.5 md:px-4 md:py-2 text-sm md:text-base rounded-zen hover:bg-zen-red dark:hover:bg-zen-accent transition-all transform hover:-translate-y-0.5 shadow-zen-hover font-zen"
            >
              Logout
            </button>
          </div>
        )}
      </div>
    </nav>
  )
}

import { useEffect, useState, useRef, useMemo } from "react";
import AudioVisualizer from './AudioVisualizer'
import { questionsData } from '../constants.js';


export default function ToolPanel({ isSessionActive, sendClientEvent, events, remoteStream, onQuestionSelected, trialMode = false, presetQuestion = null }) {
  const [functionCallOutput, setFunctionCallOutput] = useState(null);
  const [selectedDifficulty, setSelectedDifficulty] = useState("medium");
  const hasSentInstructionsRef = useRef(false);

  const [customProblem, setCustomProblem] = useState({
    title: "",
    description: "",
    example: ""
  });
  const [useCustomProblem, setUseCustomProblem] = useState(false);

  // Memoize the custom question event to avoid dependency array issues
  const customQuestionEvent = useMemo(() => {
    return events.find(e => e.type === 'session.update' && e.session?.customQuestion);
  }, [events]);

  // When session starts, pick a random question based on selected difficulty or use custom problem.
  useEffect(() => {
    if (isSessionActive && !hasSentInstructionsRef.current) {
      // In trial mode, use the preset question
      if (trialMode && presetQuestion) {
        const questionText = `Title: ${presetQuestion.title}\nDescription: ${presetQuestion.description}\nExample: ${presetQuestion.example}`;
        const instructions = `You are an expert technical interviewer. The candidate will be solving this problem: ${questionText}. nAfter stating the question, ask the candidate whether they'd like any clarification or hints before they begin. Your job is to guide and evaluate the candidate, not to solve the problem for them. Maintain a professional but intense demeanor — the bar is high here. DO not suggest more efficient solutions or approaches unless the candidate indicates that they are stuck. Simply prod them to think deeper. You will periodically be given the code the user is typing as well. Do not evaluate it unless you see some obvious mistakes or if the user asks you for help. Stay silent and let the candidate think for themselves as much as possible. Just say "that's a good idea" or "let's try that" if they're on the right track instead of giving them the full solution.`

        sendClientEvent({ type: 'session.update', session: { instructions } });
        setFunctionCallOutput(JSON.stringify({ question: questionText }));

        hasSentInstructionsRef.current = true;
        return;
      }
      // Use the memoized custom question event

      let questionText;
      let questionData;

      if (useCustomProblem && customProblem.title && customProblem.description) {
        // Use the manually entered custom problem
        questionText = `Title: ${customProblem.title}\nDescription: ${customProblem.description}\nExample: ${customProblem.example || 'No example provided'}`;
        questionData = {
          id: 'custom-' + Date.now(),
          title: customProblem.title,
          difficulty: selectedDifficulty,
          description: customProblem.description,
          example: customProblem.example || 'No example provided'
        };


      } else if (customQuestionEvent && customQuestionEvent.session.customQuestion) {
        // Use the custom question from the event
        const customQ = customQuestionEvent.session.customQuestion;
        questionText = `Title: ${customQ.title}\nDescription: ${customQ.description}\nExample: ${customQ.example}`;
        questionData = {
          id: customQ.id || 'custom',
          title: customQ.title,
          difficulty: customQ.difficulty || 'medium',
          description: customQ.description,
          example: customQ.example,
          leetcodeUrl: customQ.leetcodeUrl
        };


      } else {
        // Use a random question from the selected difficulty
        const questions = questionsData[selectedDifficulty];
        const randomQuestion = questions[Math.floor(Math.random() * questions.length)];
        questionText = `Title: ${randomQuestion.title}\nDescription: ${randomQuestion.description}\nExample: ${randomQuestion.example}`;
        questionData = {
          id: randomQuestion.id,
          title: randomQuestion.title,
          difficulty: selectedDifficulty,
          description: randomQuestion.description,
          example: randomQuestion.example
        };


      }

      const instructions = `You are a cutting-edge AI interviewer. You are an expert TECHNICAL INTERVIEWER at a fast-paced startup known for its brutal and exacting interview standards. Start by welcoming the candidate, and then dive right into the interview. Then, present EXACTLY the following technical interview question that you are provided:\n\n${questionText}\n\nAfter stating the question, ask the candidate whether they'd like any clarification or hints before they begin. Your job is to guide and evaluate the candidate, not to solve the problem for them. Maintain a professional but intense demeanor — the bar is high here. DO not suggest more efficient solutions or approaches unless the candidate indicates that they are stuck. Simply prod them to think deeper. You will periodically be given the code the user is typing as well. Do not evaluate it unless you see some obvious mistakes or if the user asks you for help. Stay silent and let the candidate think for themselves as much as possible. Just say "that's a good idea" or "let's try that" if they're on the right track instead of giving them the full solution.`;

      sendClientEvent({ type: 'session.update', session: { instructions } });
      setFunctionCallOutput(JSON.stringify({ question: questionText }));

      // Pass the selected question back to the parent component
      if (onQuestionSelected) {
        onQuestionSelected(questionData);
      }

      hasSentInstructionsRef.current = true;
    } else if (!isSessionActive) {
      hasSentInstructionsRef.current = false;

      // Reset the question when the session ends
      if (onQuestionSelected) {
        onQuestionSelected(null);
      }
    }
  }, [isSessionActive, selectedDifficulty, onQuestionSelected, sendClientEvent, trialMode, useCustomProblem, customProblem.title, customProblem.description, customProblem.example, customQuestionEvent, presetQuestion]);



  return (
    <section className="h-full w-full flex flex-col gap-4">
      {!isSessionActive && !trialMode && (
        <>
          <div className="mb-4 p-4 bg-zen-paper/80 dark:bg-zen-ink/80 backdrop-blur-sm rounded-zen border-l-4 border-zen-accent">
            <label className="font-zen text-zen-ink dark:text-zen-paper block mb-2">Challenge Level: </label>
            <select
              value={selectedDifficulty}
              onChange={(e) => {
                setSelectedDifficulty(e.target.value);
                // Reset question when difficulty changes if session is active.
                if (isSessionActive) setFunctionCallOutput(null);
              }}
              className="w-full bg-zen-paper dark:bg-zen-ink text-zen-ink dark:text-zen-paper border border-zen-accent/50 rounded-zen px-3 py-2 font-zen"
            >
              <option value="easy">Novice Path</option>
              <option value="medium">Warrior Path</option>
              <option value="hard">Master Path</option>
            </select>
          </div>

          <div className="mb-4 p-4 bg-zen-paper/80 dark:bg-zen-ink/80 backdrop-blur-sm rounded-zen border-l-4 border-zen-accent">
            <div className="flex items-center mb-3">
              <input
                type="checkbox"
                id="use-custom-problem"
                checked={useCustomProblem}
                onChange={(e) => setUseCustomProblem(e.target.checked)}
                className="mr-2 h-4 w-4 accent-zen-accent"
              />
              <label htmlFor="use-custom-problem" className="font-zen text-zen-ink dark:text-zen-paper">
                Use Custom Problem
              </label>
            </div>

            {useCustomProblem && (
              <div className="space-y-3">
                <div>
                  <label className="font-zen text-zen-ink dark:text-zen-paper block mb-1 text-sm">Problem Title:</label>
                  <input
                    type="text"
                    value={customProblem.title}
                    onChange={(e) => setCustomProblem({...customProblem, title: e.target.value})}
                    placeholder="Enter problem title"
                    className="w-full bg-zen-paper dark:bg-zen-ink text-zen-ink dark:text-zen-paper border border-zen-accent/50 rounded-zen px-3 py-2 font-zen text-sm"
                  />
                </div>

                <div>
                  <label className="font-zen text-zen-ink dark:text-zen-paper block mb-1 text-sm">Problem Description:</label>
                  <textarea
                    value={customProblem.description}
                    onChange={(e) => setCustomProblem({...customProblem, description: e.target.value})}
                    placeholder="Paste problem description here"
                    rows={5}
                    className="w-full bg-zen-paper dark:bg-zen-ink text-zen-ink dark:text-zen-paper border border-zen-accent/50 rounded-zen px-3 py-2 font-zen text-sm"
                  />
                </div>

                <div>
                  <label className="font-zen text-zen-ink dark:text-zen-paper block mb-1 text-sm">Example (Optional):</label>
                  <textarea
                    value={customProblem.example}
                    onChange={(e) => setCustomProblem({...customProblem, example: e.target.value})}
                    placeholder="Paste example input/output here"
                    rows={3}
                    className="w-full bg-zen-paper dark:bg-zen-ink text-zen-ink dark:text-zen-paper border border-zen-accent/50 rounded-zen px-3 py-2 font-zen text-sm"
                  />
                </div>

                <p className="text-xs text-zen-ink/70 dark:text-zen-paper/70 italic">
                  Paste any problem description from LeetCode, HackerRank, or any other source.
                </p>
              </div>
            )}
          </div>
        </>
      )}

      <div className="flex-1 bg-zen-paper/90 dark:bg-zen-ink/90 rounded-zen p-4 md:p-6 shadow-zen border border-zen-accent/20 overflow-y-auto">
        <h2 className="text-xl font-zen text-zen-ink dark:text-zen-paper mb-4 pb-2 border-b border-zen-accent/30">
          {trialMode ? "Interview Challenge" : "Samurai Training Ground"}
        </h2>
        {isSessionActive ? (
          functionCallOutput ? (
            <InterviewQuestionOutput functionCallOutput={functionCallOutput} remoteStream={remoteStream} />
          ) : (
            <p className="font-zen text-zen-ink/70 dark:text-zen-paper/70 italic">Preparing your challenge...</p>
          )
        ) : (
          <div className="text-center py-8">
            <div className="relative mb-8">
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="w-24 h-24 rounded-full bg-zen-accent/10 dark:bg-zen-purple/10 animate-pulse"></div>
              </div>
              <div className="relative">
                <svg className="w-20 h-20 mx-auto text-zen-accent dark:text-zen-purple" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="currentColor" strokeWidth="2" />
                  <path d="M12 16V12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
                  <path d="M12 8H12.01" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
                </svg>
              </div>
            </div>

            <div className="max-w-lg mx-auto px-4">
              <div className="bg-zen-paper/50 dark:bg-zen-ink/50 p-6 rounded-zen border border-zen-accent/20 backdrop-blur-sm">
                {trialMode ? (
                  <>
                    <ul className="text-left font-zen text-zen-ink dark:text-zen-paper text-base space-y-4">
                      <li className="flex items-center gap-3">
                        <span className="text-2xl">🎯</span>
                        <span>Experience a realistic technical interview</span>
                      </li>
                      <li className="flex items-center gap-3">
                        <span className="text-2xl">💡</span>
                        <span>Get real-time guidance and detailed feedback</span>
                      </li>
                      <li className="flex items-center gap-3">
                        <span className="text-2xl">📝</span>
                        <span>You'll receive a medium-level coding challenge</span>
                      </li>
                      <li className="flex items-center gap-3">
                        <span className="text-2xl">⏱️</span>
                        <span>30 minutes to solve and discuss your approach</span>
                      </li>
                    </ul>
                    <p className="text-center mt-6 font-zen text-zen-ink dark:text-zen-paper text-sm">
                      Click "Begin Training" to start your free trial
                    </p>
                  </>
                ) : (
                  <>
                    <ul className="text-left font-zen text-zen-ink dark:text-zen-paper text-base space-y-4">
                      <li className="flex items-center gap-3">
                        <span className="text-2xl">⚔️</span>
                        <span>Experience a realistic technical interview with our all-knowing sensei</span>
                      </li>
                      <li className="flex items-center gap-3">
                        <span className="text-2xl">🎯</span>
                        <span>Choose from preset challenges or create your own custom question</span>
                      </li>
                      <li className="flex items-center gap-3">
                        <span className="text-2xl">💡</span>
                        <span>Get real-time guidance and detailed feedback from your sensei</span>
                      </li>
                      <li className="flex items-center gap-3">
                        <span className="text-2xl">📈</span>
                        <span>Improve your interview skills with each session</span>
                      </li>
                    </ul>
                    <p className="text-center mt-6 font-zen text-zen-ink dark:text-zen-paper text-sm">
                      Click "Begin Training" to begin
                    </p>
                  </>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </section>
  );
}

function InterviewQuestionOutput({ functionCallOutput, remoteStream }) {
  if (!functionCallOutput) return <div className="font-zen text-zen-red">No challenge available.</div>;
  let parsed;
  try {
    parsed = JSON.parse(functionCallOutput);
  } catch (err) {
    console.error("Error parsing functionCallOutput:", err);
    return <div className="font-zen text-zen-red">Error parsing challenge data.</div>;
  }
  const { question } = parsed;
  return (
    <div className="space-y-6">
      <div>
        <h3 className="font-zen text-zen-ink dark:text-zen-paper mb-3 text-lg">Your Challenge:</h3>
        <div className="font-code text-zen-ink dark:text-zen-paper space-y-2">
          {question.split('\n').map((line, index) => (
            <p key={index} className="text-sm md:text-base">
              {line}
            </p>
          ))}
        </div>
      </div>
      {remoteStream && (
        <>
          <div className="h-px bg-zen-accent/20" />
          <div>
            <h3 className="font-zen text-zen-ink dark:text-zen-paper mb-3 text-lg">Sensei's Guidance:</h3>
            <div className="flex items-center justify-center">
              <AudioVisualizer stream={remoteStream} />
            </div>
          </div>
        </>
      )}
    </div>
  );
}

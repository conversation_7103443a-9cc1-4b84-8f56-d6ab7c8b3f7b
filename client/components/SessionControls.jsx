import { useEffect, useRef, useState } from "react";
import Button from "./Button";

function useInterval(callback, delay) {
  const savedCallback = useRef();
  useEffect(() => { savedCallback.current = callback; }, [callback]);
  useEffect(() => {
    if (delay !== null) {
      const tick = () => savedCallback.current();
      const id = setInterval(tick, delay);
      return () => clearInterval(id);
    }
  }, [delay]);
}

function SessionStopped({ startSession }) {
  const [isActivating, setIsActivating] = useState(false);
  function handleStartSession() {
    if (isActivating) return;
    setIsActivating(true);
    startSession();
  }
  return (
    <div className="flex items-center justify-center w-full h-full">
      <Button
        onClick={handleStartSession}
        className={isActivating ? "bg-zen-ink/70" : "bg-zen-highlight dark:bg-zen-accent"}
        icon={<span className="text-xl">⚔️</span>}
      >
        {isActivating ? "Summoning sensei..." : "Begin Training"}
      </Button>
    </div>
  );
}

function SessionActive({ stopSession, sendTextMessage, code }) {
  useInterval(() => {
    sendTextMessage(code + "Here is the latest code from the user. Do not react to it unless the user asks you to discuss it.");
  }, 5000); // auto-send code every 5 seconds

  return (
    <div className="flex items-center justify-center w-full h-full">
      <Button
        onClick={stopSession}
        className="bg-zen-ink/80 dark:bg-zen-purple/80 hover:bg-zen-red dark:hover:bg-zen-red"
        icon={<span className="text-xl">⛔</span>}
      >
        End Training
      </Button>
    </div>
  );
}

export default function SessionControls({
  startSession,
  stopSession,
  sendTextMessage,
  isSessionActive,
  code,
}) {
  return (
    <div className="flex gap-4 border-t border-zen-accent/30 dark:border-zen-accent/20 h-full rounded-zen">
      {isSessionActive ? (
        <SessionActive stopSession={stopSession} sendTextMessage={sendTextMessage} code={code} />
      ) : (
        <SessionStopped startSession={startSession} />
      )}
    </div>
  );
}

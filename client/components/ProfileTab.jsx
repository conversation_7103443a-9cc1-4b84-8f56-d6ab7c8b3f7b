import { useState, useEffect } from 'react'
import { getUserFeedback, getBatchQuestionPercentiles } from '../../api/firebase-api'
import { doc, onSnapshot } from 'firebase/firestore'
import { db } from '../../firebase'
import DetailedFeedbackCard from './DetailedFeedbackCard'
import { getScoreColor, getScoreEmoji } from '../utils/styling'

export default function ProfileTab({ user }) {
  const [feedbackList, setFeedbackList] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [userCredits, setUserCredits] = useState(0)
  const [percentileData, setPercentileData] = useState({})
  
  // Pagination and filter states
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(0)
  const [difficulty, setDifficulty] = useState('')
  const [sortBy, setSortBy] = useState('timestamp')
  const [sortOrder, setSortOrder] = useState('desc')
  const [pageSize, setPageSize] = useState(10)

  const fetchUserFeedback = async () => {
    if (!user?.uid) return

    try {
      setLoading(true)
      setError(null)

      console.log('Fetching feedback for page:', currentPage)

      const { feedback, total, totalPages: pages } = await getUserFeedback(user.uid, {
        page: currentPage,
        pageSize,
        difficulty,
        sortBy,
        sortOrder
      })

      console.log('Received feedback:', feedback.length, 'items')
      console.log('Total pages:', pages)

      setFeedbackList(feedback)
      setTotalPages(pages)

      // Fetch percentile data for all feedback entries in a single batch
      const items = feedback
        .filter(item => item.questionId && item.score !== undefined)
        .map(item => ({ questionId: item.questionId, score: item.score }))
      
      const percentileResults = await getBatchQuestionPercentiles(items)
      const newPercentileData = feedback.reduce((acc, item) => {
        if (item.questionId && item.score !== undefined) {
          acc[item.id] = percentileResults[item.questionId] || { percentile: 0, totalAttempts: 0 }
        } else {
          acc[item.id] = { percentile: 0, totalAttempts: 0 }
        }
        return acc
      }, {})
      
      setPercentileData(newPercentileData)
    } catch (err) {
      console.error('Error in fetchUserFeedback:', err)
      setError(err.message || 'Failed to load your feedback history. Please try again later.')
    } finally {
      setLoading(false)
    }
  }

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1)
  }, [difficulty, sortBy, sortOrder, pageSize])

  // Fetch data when page or filters change
  useEffect(() => {
    fetchUserFeedback()
  }, [user, currentPage, pageSize, difficulty, sortBy, sortOrder])

  useEffect(() => {
    // Subscribe to user document for credits updates
    if (user?.uid) {
      const unsubscribe = onSnapshot(doc(db, "users", user.uid), (doc) => {
        if (doc.exists()) {
          setUserCredits(doc.data().credits || 0);
        }
      });
      return () => unsubscribe();
    }
  }, [user]);

  // Format timestamp to a readable date
  const formatDate = (timestamp) => {
    if (!timestamp) return 'Unknown date'

    // If it's a Firestore timestamp, convert to JS Date
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp)

    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date)
  }

  // Format difficulty level to be more readable
  const formatDifficulty = (difficulty) => {
    if (!difficulty || difficulty === 'unknown') return 'Unknown'

    const difficultyMap = {
      'easy': 'Novice Path',
      'medium': 'Warrior Path',
      'hard': 'Master Path'
    }

    return difficultyMap[difficulty] || difficulty.charAt(0).toUpperCase() + difficulty.slice(1)
  }

  const handleDifficultyChange = (e) => {
    setDifficulty(e.target.value)
  }

  const handleSortChange = (e) => {
    const [field, order] = e.target.value.split('-')
    setSortBy(field)
    setSortOrder(order)
  }

  const handlePageSizeChange = (e) => {
    setPageSize(Number(e.target.value))
  }

  const handlePageChange = (newPage) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setCurrentPage(newPage)
    }
  }

  return (
    <div className="h-full w-full overflow-y-auto p-4 md:p-6 bg-zen-pattern dark:bg-samurai-pattern">
      <div className="bg-zen-paper dark:bg-zen-ink rounded-zen shadow-zen p-4 md:p-6 mb-6 border border-zen-accent/20">
        <div className="flex flex-col md:flex-row md:items-center gap-4 mb-4 pb-4 border-b border-zen-accent/30">
          <div className="flex items-center gap-3">
            {user?.photoURL && (
              <img
                src={user.photoURL}
                alt="Profile"
                className="w-12 h-12 rounded-full border-2 border-zen-accent"
              />
            )}
            <div>
              <h2 className="text-xl md:text-2xl font-zen text-zen-ink dark:text-zen-paper">
                {user?.displayName || 'Samurai'}
              </h2>
              <p className="text-sm text-zen-ink/70 dark:text-zen-paper/70 font-zen">
                {user?.email || ''}
              </p>
              <p className="text-sm text-zen-accent font-zen mt-1">
                Credits: {userCredits}
              </p>
            </div>
          </div>
        </div>

        <h3 className="text-lg font-zen text-zen-ink dark:text-zen-paper mb-4 border-b border-zen-accent/20 pb-2">
          Your Training History
        </h3>

        {/* Filter Controls */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <select
            value={difficulty}
            onChange={handleDifficultyChange}
            className="px-4 py-2 rounded-zen border border-zen-accent/30 bg-zen-paper dark:bg-zen-ink text-zen-ink dark:text-zen-paper font-zen focus:outline-none focus:border-zen-accent"
          >
            <option value="">All Difficulties</option>
            <option value="easy">Novice Path</option>
            <option value="medium">Warrior Path</option>
            <option value="hard">Master Path</option>
          </select>
          <select
            value={`${sortBy}-${sortOrder}`}
            onChange={handleSortChange}
            className="px-4 py-2 rounded-zen border border-zen-accent/30 bg-zen-paper dark:bg-zen-ink text-zen-ink dark:text-zen-paper font-zen focus:outline-none focus:border-zen-accent"
          >
            <option value="timestamp-desc">Newest First</option>
            <option value="timestamp-asc">Oldest First</option>
            <option value="score-desc">Highest Score</option>
            <option value="score-asc">Lowest Score</option>
          </select>
        </div>

        {loading && feedbackList.length === 0 && (
          <div className="flex justify-center items-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-zen-accent"></div>
          </div>
        )}

        {error && (
          <div className="bg-zen-red/10 border border-zen-red/30 rounded-zen p-4 mb-4">
            <p className="text-zen-red font-zen">{error}</p>
            <button 
              onClick={() => fetchUserFeedback()}
              className="mt-2 px-4 py-2 bg-zen-red/20 hover:bg-zen-red/30 text-zen-red rounded-zen font-zen transition-colors"
            >
              Try Again
            </button>
          </div>
        )}

        {!loading && !error && feedbackList.length === 0 && (
          <div className="text-center py-8 border border-dashed border-zen-accent/30 rounded-zen">
            <p className="font-zen text-zen-ink/70 dark:text-zen-paper/70">
              No feedback found matching your criteria.
            </p>
          </div>
        )}

        {!loading && !error && feedbackList.length > 0 && (
          <>
            <div className="overflow-x-auto mb-4">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="border-b border-zen-accent/30">
                    <th className="text-left p-2 font-zen text-zen-ink dark:text-zen-paper">Date</th>
                    <th className="text-left p-2 font-zen text-zen-ink dark:text-zen-paper">Question</th>
                    <th className="text-left p-2 font-zen text-zen-ink dark:text-zen-paper">Difficulty</th>
                    <th className="text-left p-2 font-zen text-zen-ink dark:text-zen-paper">Score</th>
                    <th className="text-left p-2 font-zen text-zen-ink dark:text-zen-paper">Percentile</th>
                  </tr>
                </thead>
                <tbody>
                  {feedbackList.map((feedback) => (
                    <tr
                      key={feedback.id}
                      className="border-b border-zen-accent/10 hover:bg-zen-accent/5 dark:hover:bg-zen-accent/10 transition-colors"
                    >
                      <td className="p-2 font-zen text-zen-ink dark:text-zen-paper text-sm">
                        {formatDate(feedback.timestamp)}
                      </td>
                      <td className="p-2 font-zen text-zen-ink dark:text-zen-paper">
                        {feedback.questionTitle || 'Unknown Question'}
                      </td>
                      <td className="p-2 font-zen text-zen-ink dark:text-zen-paper">
                        {formatDifficulty(feedback.difficulty)}
                      </td>
                      <td className="p-2 font-zen">
                        <span className="text-zen-highlight dark:text-zen-accent font-bold">
                          {feedback.score}
                        </span>
                        <span className="text-zen-ink/50 dark:text-zen-paper/50 text-sm ml-1">/ 100</span>
                      </td>
                      <td className="p-2 font-zen">
                        {percentileData[feedback.id]?.percentile !== undefined ? (
                          <div className="flex flex-col">
                            <span className={`font-bold ${getScoreColor(percentileData[feedback.id].percentile, 100)}`}>
                              {getScoreEmoji(percentileData[feedback.id].percentile, 100)} Better than {Math.round(percentileData[feedback.id].percentile)}% of users
                            </span>
                          </div>
                        ) : (
                          <span className="text-zen-ink/50 dark:text-zen-paper/50 text-sm">-</span>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination Controls */}
            <div className="flex flex-col md:flex-row justify-between items-center gap-4 mt-4 pt-4 border-t border-zen-accent/20">
              <div className="flex items-center gap-2">
                <span className="text-sm text-zen-ink/70 dark:text-zen-paper/70 font-zen">Show:</span>
                <select
                  value={pageSize}
                  onChange={handlePageSizeChange}
                  className="px-2 py-1 rounded-zen border border-zen-accent/30 bg-zen-paper dark:bg-zen-ink text-zen-ink dark:text-zen-paper font-zen text-sm focus:outline-none focus:border-zen-accent"
                >
                  <option value="5">5</option>
                  <option value="10">10</option>
                  <option value="20">20</option>
                  <option value="50">50</option>
                </select>
                <span className="text-sm text-zen-ink/70 dark:text-zen-paper/70 font-zen">per page</span>
              </div>

              <div className="flex items-center gap-2">
                <button
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                  className="px-3 py-1 rounded-zen border border-zen-accent/30 bg-zen-paper dark:bg-zen-ink text-zen-ink dark:text-zen-paper font-zen text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-zen-accent/10"
                >
                  Previous
                </button>
                <span className="text-sm text-zen-ink/70 dark:text-zen-paper/70 font-zen">
                  Page {currentPage} of {totalPages}
                </span>
                <button
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  className="px-3 py-1 rounded-zen border border-zen-accent/30 bg-zen-paper dark:bg-zen-ink text-zen-ink dark:text-zen-paper font-zen text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-zen-accent/10"
                >
                  Next
                </button>
              </div>
            </div>
          </>
        )}
      </div>

      {!loading && !error && feedbackList.length > 0 && (
        <div className="bg-zen-paper dark:bg-zen-ink rounded-zen shadow-zen p-4 md:p-6 border border-zen-accent/20">
          <h3 className="text-lg font-zen text-zen-ink dark:text-zen-paper mb-4 border-b border-zen-accent/20 pb-2">
            Detailed Feedback
          </h3>

          {feedbackList.map((feedback) => (
            <DetailedFeedbackCard
              key={`detail-${feedback.id}`}
              feedback={feedback}
              formatDate={formatDate}
            />
          ))}
        </div>
      )}
    </div>
  )
}

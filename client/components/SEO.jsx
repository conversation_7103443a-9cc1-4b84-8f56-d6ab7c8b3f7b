import { Helmet } from 'react-helmet-async';

const SEO = ({ 
  title = 'Samurai Interview - Master Your Interview Skills',
  description = 'Prepare for your interviews with our samurai-inspired training platform. Get real-time feedback and improve your interview skills with Samurai Interview.',
  keywords = 'interview preparation, mock interview, technical interview, job interview, career preparation',
  ogImage = '/assets/og-image.jpg',
  ogUrl = 'https://samuraiinterview.com',
  ogType = 'website',
  twitterCard = 'summary_large_image'
}) => {
  return (
    <Helmet>
      {/* Basic Meta Tags */}
      <title>{title}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords} />
      <link rel="canonical" href={ogUrl} />

      {/* Open Graph / Facebook */}
      <meta property="og:type" content={ogType} />
      <meta property="og:url" content={ogUrl} />
      <meta property="og:title" content={title} />
      <meta property="og:description" content={description} />
      <meta property="og:image" content={ogImage} />

      {/* Twitter */}
      <meta property="twitter:card" content={twitterCard} />
      <meta property="twitter:url" content={ogUrl} />
      <meta property="twitter:title" content={title} />
      <meta property="twitter:description" content={description} />
      <meta property="twitter:image" content={ogImage} />

      {/* Additional SEO Meta Tags */}
      <meta name="robots" content="index, follow" />
      <meta name="theme-color" content="#ffffff" />
    </Helmet>
  );
};

export default SEO; 
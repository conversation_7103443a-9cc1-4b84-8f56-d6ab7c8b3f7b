import React, { useState } from 'react';
import Terms from '../pages/Terms';
import Privacy from '../pages/Privacy';

const Footer = () => {
  const [showModal, setShowModal] = useState(false);
  const [modalContent, setModalContent] = useState(null);

  const openModal = (content) => {
    setModalContent(content);
    setShowModal(true);
  };

  const closeModal = () => {
    setShowModal(false);
    setModalContent(null);
  };

  return (
    <>
      <footer className="fixed bottom-0 w-full backdrop-blur-sm border-gray-200 py-2 px-4 text-center text-sm text-gray-600">
        <div className="max-w-7xl mx-auto flex justify-end space-x-4 pr-4">
          <span className="text-gray-600">
            © {new Date().getFullYear()} Samurai Interview
          </span>
          <span>•</span>
          <span 
            onClick={() => openModal('terms')}
            className="hover:text-gray-900 cursor-pointer"
          >
            Terms of Service
          </span>
          <span>•</span>
          <span 
            onClick={() => openModal('privacy')}
            className="hover:text-gray-900 cursor-pointer"
          >
            Privacy Policy
          </span>
        </div>
      </footer>

      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-4xl max-h-[90vh] overflow-y-auto relative">
            <button 
              onClick={closeModal}
              className="absolute top-4 right-4 text-gray-500 hover:text-gray-700"
            >
              ✕
            </button>
            {modalContent === 'terms' ? <Terms /> : <Privacy />}
          </div>
        </div>
      )}
    </>
  );
};

export default Footer; 
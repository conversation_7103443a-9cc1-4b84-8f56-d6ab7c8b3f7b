export default function Button({ icon, children, onClick, className }) {
  return (
    <button
      className={`bg-zen-ink dark:bg-zen-purple text-zen-paper rounded-zen p-4 flex items-center gap-2 hover:bg-zen-highlight dark:hover:bg-zen-accent transition-all transform hover:-translate-y-1 shadow-zen hover:shadow-zen-hover font-zen ${className}`}
      onClick={onClick}
    >
      {icon}
      {children}
    </button>
  );
}

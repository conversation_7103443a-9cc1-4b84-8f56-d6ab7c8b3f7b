import { useState } from 'react'
import { addDoc, collection, onSnapshot } from 'firebase/firestore'

/**
 * Modal component for purchasing credits
 */
export default function CreditModal({ isOpen, onClose, db, user, activeTab }) {
  const [isCreatingCheckout, setIsCreatingCheckout] = useState(false)

  if (!isOpen) return null

  /**
   * Create a checkout session with Stripe
   * @param {string} selectedPriceId - The Stripe price ID
   */
  async function createTestCheckout(selectedPriceId) {
    if (!user?.uid) {
      alert("Not logged in")
      return
    }

    // Add validation for price ID
    if (!selectedPriceId) {
      alert('Error: No price ID available. Please check your environment variables.')
      return
    }

    setIsCreatingCheckout(true)

    try {
      // Build success URL with tab information
      const successUrl = new URL('/success', window.location.origin)
      // Add the active tab as a query parameter if available
      if (activeTab) {
        successUrl.searchParams.append('tab', activeTab)
      }

      // Build cancel URL with tab information
      const cancelUrl = new URL('/cancel', window.location.origin)
      // Add the active tab as a query parameter if available
      if (activeTab) {
        cancelUrl.searchParams.append('tab', activeTab)
      }

      // 1) write a new checkout session
      const docRef = await addDoc(
        collection(db, "users", user.uid, "checkout_sessions"),
        {
          price: selectedPriceId,
          success_url: successUrl.toString(),
          mode: 'payment',
          cancel_url: cancelUrl.toString(),
        }
      )

      // 2) listen for the extension to write back `url`
      onSnapshot(docRef, (snap) => {
        const data = snap.data() || {}
        if (data.url) {
          window.location.assign(data.url)  // send them to Stripe Checkout
        }
      })
    } catch (error) {
      alert('Failed to create checkout session. Please try again.')
      setIsCreatingCheckout(false)
    }
  }

  return (
    <>
      <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
        <div className="bg-zen-paper dark:bg-zen-ink p-6 rounded-zen shadow-zen border border-zen-accent/20 w-full max-w-md mx-4">
          <h2 className="text-xl font-zen text-zen-ink dark:text-zen-paper mb-4">Choose Your Path</h2>
          <div className="space-y-4">
            <button
              onClick={() => {
                const priceId = import.meta.env.VITE_STRIPE_PRICE_BASIC
                createTestCheckout(priceId)
              }}
              disabled={isCreatingCheckout}
              className="w-full p-4 border border-zen-accent/30 rounded-zen hover:border-zen-accent hover:bg-zen-accent/5 dark:hover:bg-zen-accent/10 transition-all disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <div className="flex justify-between items-center">
                <div>
                  <h3 className="font-zen text-zen-ink dark:text-zen-paper">Novice Package</h3>
                  <p className="text-sm text-zen-ink/70 dark:text-zen-paper/70">3 Credits</p>
                </div>
                <span className="font-zen text-zen-accent">$5</span>
              </div>
            </button>

            <button
              onClick={() => {
                const priceId = import.meta.env.VITE_STRIPE_PRICE_PRO
                createTestCheckout(priceId)
              }}
              disabled={isCreatingCheckout}
              className="w-full p-4 border border-zen-accent/30 rounded-zen hover:border-zen-accent hover:bg-zen-accent/5 dark:hover:bg-zen-accent/10 transition-all disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <div className="flex justify-between items-center">
                <div>
                  <h3 className="font-zen text-zen-ink dark:text-zen-paper">Warrior Package</h3>
                  <p className="text-sm text-zen-ink/70 dark:text-zen-paper/70">10 Credits</p>
                </div>
                <span className="font-zen text-zen-accent">$45</span>
              </div>
            </button>

            <button
              onClick={() => {
                const priceId = import.meta.env.VITE_STRIPE_PRICE_MASTER
                createTestCheckout(priceId)
              }}
              disabled={isCreatingCheckout}
              className="w-full p-4 border border-zen-accent/30 rounded-zen hover:border-zen-accent hover:bg-zen-accent/5 dark:hover:bg-zen-accent/10 transition-all disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <div className="flex justify-between items-center">
                <div>
                  <h3 className="font-zen text-zen-ink dark:text-zen-paper">Mastery Package</h3>
                  <p className="text-sm text-zen-ink/70 dark:text-zen-paper/70">25 Credits</p>
                </div>
                <span className="font-zen text-zen-accent">$100</span>
              </div>
            </button>
          </div>
          <button
            onClick={onClose}
            disabled={isCreatingCheckout}
            className="mt-4 w-full p-2 text-zen-ink/70 dark:text-zen-paper/70 hover:text-zen-ink dark:hover:text-zen-paper transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Cancel
          </button>
        </div>
      </div>

      {/* Loading Overlay */}
      {isCreatingCheckout && (
        <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-[60]">
          <div className="bg-zen-paper dark:bg-zen-ink p-8 rounded-zen shadow-zen flex flex-col items-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-zen-accent mb-4"></div>
            <p className="text-zen-ink dark:text-zen-paper font-zen text-lg">Loading...</p>
          </div>
        </div>
      )}
    </>
  )
}

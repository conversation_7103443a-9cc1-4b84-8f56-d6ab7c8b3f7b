import * as Sentry from "@sentry/react";
import { useState, useEffect } from 'react'
import { onAuthStateChanged } from 'firebase/auth'
import { auth } from '../../firebase'
import Login from './Login'
import InterviewGym from './InterviewGym'
import Footer from './Footer'
import { initGA, trackPageView } from '../utils/analytics'

if (process.env.NODE_ENV === 'production') {
  Sentry.init({
    dsn: "https://<EMAIL>/4509429523152896",
    sendDefaultPii: true,
    integrations: [
      Sentry.replayIntegration({
        // Mask all text content to protect user privacy
        maskAllText: true,
        // Block all media elements (images, videos) to protect user privacy
        blockAllMedia: true,
        // Record user interactions like clicks, form inputs, etc.
        recordClicks: true,
        // Record form inputs
        recordInputs: true,
        // Record DOM mutations
        recordDOM: true,
        // Record navigation events
        recordNavigation: true,
        // Record console logs
        recordConsole: true,
        // Record network requests
        recordNetwork: true,
      })
    ],
    // Increase session sample rate to capture more user sessions
    replaysSessionSampleRate: 0.9, // Capture 90% of all sessions
    // Always capture sessions where errors occur
    replaysOnErrorSampleRate: 1.0
  });
}

export default function App() {
  const [user, setUser] = useState(null)
  const [loading, setLoading] = useState(true)

  // Extract tab from URL query parameters - safely check if we're in browser
  const getInitialTab = () => {
    if (typeof window !== 'undefined') {
      const searchParams = new URLSearchParams(window.location.search)
      return searchParams.get('tab') || 'interview'
    }
    return 'interview'
  }

  // Initialize Google Analytics
  useEffect(() => {
    if (typeof window !== 'undefined') {
      initGA()
    }
  }, [])

  // Track page views
  useEffect(() => {
    if (typeof window !== 'undefined') {
      trackPageView(window.location.pathname + window.location.search)
    }
  }, [])

  // Check for logged-in user on page load
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (currentUser) => {
      setUser(currentUser)
      setLoading(false)
    })

    // Cleanup subscription on unmount
    return () => unsubscribe()
  }, [])

  // Show loading state while checking authentication
  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen bg-zen-paper dark:bg-zen-ink">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-zen-accent"></div>
      </div>
    )
  }

  if (user) {
    // Pass the initial tab from URL to InterviewGym
    const initialTab = getInitialTab()
    return (
      <InterviewGym user={user} initialTab={initialTab} />
    )
  }

  return (
    <>
      <Login onLogin={setUser} />
      <Footer />
    </>
  )
}

import React from 'react';

const Privacy = () => {
  return (
    <div className="max-w-4xl mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8">Privacy Policy</h1>
      <div className="prose prose-sm">
        <h2>1. Who we are</h2>
        <p>Samurai Interview ("we", "us", "our") is an online platform that delivers real-time mock technical interviews. Our contact address is Toronto, Canada. You can reach us at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>

        <h2>2. Data we collect</h2>
        <ul>
          <li><strong>Account data</strong> – email address you provide at sign‑up.</li>
          <li><strong>Session data</strong> – audio streamed during an interview, the resulting transcript, evaluation metrics, and session metadata (timestamps, credit balance).</li>
          <li><strong>Payment data</strong> – handled by <PERSON><PERSON>; we receive only non‑card identifiers (customer ID, charge ID, credit balance).</li>
        </ul>

        <h2>3. How we use your data</h2>
        <table className="w-full">
          <thead>
            <tr>
              <th>Purpose</th>
              <th>Legal basis (GDPR)</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>Provide and bill for interview sessions</td>
              <td>Performance of contract</td>
            </tr>
            <tr>
              <td>Improve model accuracy and service reliability</td>
              <td>Legitimate interest</td>
            </tr>
            <tr>
              <td>Detect misuse or security incidents</td>
              <td>Legitimate interest</td>
            </tr>
            <tr>
              <td>Communicate with you about your account</td>
              <td>Performance of contract</td>
            </tr>
          </tbody>
        </table>

        <h2>4. Third‑party processors</h2>
        <table className="w-full">
          <thead>
            <tr>
              <th>Service</th>
              <th>Data shared</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td><strong>Stripe</strong> (payments)</td>
              <td>email, credit purchase details</td>
            </tr>
            <tr>
              <td><strong>OpenAI</strong> (real‑time voice transcription & analysis)</td>
              <td>live audio/text</td>
            </tr>
            <tr>
              <td><strong>Deepgram</strong> (speech‑to‑text fallback)</td>
              <td>live audio</td>
            </tr>
            <tr>
              <td><strong>Firebase / Google Cloud</strong> (hosting & auth)</td>
              <td>account and session data</td>
            </tr>
          </tbody>
        </table>
        <p>All processors are bound by contracts requiring GDPR‑level safeguards.</p>

        <h2>5. Data retention</h2>
        <ul>
          <li>Account data: kept until you delete your account or 24 months of inactivity, whichever is sooner.</li>
          <li>Audio & transcripts: stored for 90 days, then deleted or anonymised.</li>
          <li>Payment records: retained for 7 years to satisfy tax obligations.</li>
        </ul>

        <h2>6. Security</h2>
        <p>We employ end‑to‑end TLS, principle‑of‑least‑privilege access controls, and encrypted storage. No method is 100% secure; use the service at your own risk.</p>

        <h2>7. International transfers</h2>
        <p>Your data may be processed outside Canada (e.g., U.S. servers). We rely on contractual clauses and equivalent safeguards.</p>

        <h2>8. Your rights</h2>
        <p>Under PIPEDA, GDPR and similar laws, you may access, correct, delete, or port your personal data. Email <a href="mailto:<EMAIL>"><EMAIL></a> (or the address above) to exercise these rights.</p>

        <h2>9. Children's privacy</h2>
        <p>Samurai Interview is <strong>not directed to children under 13</strong> and we do not knowingly collect their data. If you are a parent who believes we have collected such data, contact us for deletion.</p>

        <h2>10. Changes</h2>
        <p>We may update this Policy. Material changes will be notified via email or in‑app.</p>
      </div>
    </div>
  );
};

export default Privacy; 
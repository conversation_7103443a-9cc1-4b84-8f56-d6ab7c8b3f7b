import { useState, useEffect } from 'react'
import { onAuthStateChanged } from 'firebase/auth'
import { auth } from '../../firebase'

export default function CancelPage() {
  const [loading, setLoading] = useState(true)
  const [user, setUser] = useState(null)

  // Safe navigation function that works on both client and server
  const navigateTo = (path) => {
    if (typeof window !== 'undefined') {
      window.location.href = path
    }
  }

  // Extract tab from URL query parameters - safely check if we're in browser
  const getTabFromQuery = () => {
    if (typeof window !== 'undefined') {
      const searchParams = new URLSearchParams(window.location.search)
      return searchParams.get('tab') || 'interview'
    }
    return 'interview'
  }

  useEffect(() => {
    // Check if user is authenticated
    const unsubscribe = onAuthStateChanged(auth, (currentUser) => {
      setUser(currentUser)
      setLoading(false)

      // Redirect to main app after 3 seconds with the correct tab
      const timer = setTimeout(() => {
        const tab = getTabFromQuery()
        navigateTo(`/?tab=${tab}`)
      }, 3000)

      return () => clearTimeout(timer)
    })

    return () => unsubscribe()
  }, [navigate])

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen bg-zen-paper dark:bg-zen-ink">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-zen-accent"></div>
      </div>
    )
  }

  return (
    <div className="flex flex-col items-center justify-center h-screen bg-zen-paper dark:bg-zen-ink text-zen-ink dark:text-zen-paper p-4">
      <div className="max-w-md w-full bg-zen-paper dark:bg-zen-ink border border-zen-accent/30 rounded-zen p-8 shadow-zen">
        <h1 className="text-2xl font-zen mb-4 text-center">Payment Cancelled</h1>

        {user ? (
          <p className="mb-6 text-center">
            Your payment was cancelled, {user.displayName || 'Samurai'}. No charges were made.
          </p>
        ) : (
          <p className="mb-6 text-center">
            Your payment was cancelled. No charges were made.
          </p>
        )}

        <div className="text-center">
          <p className="text-sm text-zen-ink/70 dark:text-zen-paper/70">
            Redirecting you back to the application...
          </p>
          <button
            onClick={() => {
              const tab = getTabFromQuery()
              navigateTo(`/?tab=${tab}`)
            }}
            className="mt-4 px-6 py-2 bg-zen-accent text-zen-paper font-zen rounded-zen hover:bg-zen-accent/90 transition-all"
          >
            Return Now
          </button>
        </div>
      </div>
    </div>
  )
}

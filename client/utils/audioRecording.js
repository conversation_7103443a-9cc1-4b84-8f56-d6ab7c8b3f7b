/**
 * Utility functions for audio recording in the interview application
 */

/**
 * Start recording audio from both remote and mic streams
 * @param {MediaStream} remoteStream - The remote audio stream
 * @param {MediaStream} micStream - The local microphone stream
 * @param {Object} refs - Object containing mediaRecorderRef, audioChunksRef, and audioContextRef
 * @param {Function} setRecordingError - Function to set recording error state
 */
export const startCombinedRecording = async (
  remoteStream,
  micStream,
  refs,
  setRecordingError
) => {
  try {
    // Create a new context
    refs.audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)();
    const ctx = refs.audioContextRef.current;

    // Create input sources
    const remoteSource = ctx.createMediaStreamSource(remoteStream);
    const micSource = ctx.createMediaStreamSource(micStream);

    // Create a merger node to combine the streams
    const merger = ctx.createChannelMerger(2);

    // Connect sources to merger
    remoteSource.connect(merger, 0, 0); // Connect to left channel
    micSource.connect(merger, 0, 1); // Connect to right channel

    // Create a destination
    const dest = ctx.createMediaStreamDestination();
    merger.connect(dest);

    // Get the combined stream
    const combinedStream = dest.stream;

    // Try different MIME types in order of preference
    const mimeTypes = [
      'audio/webm',
      'audio/webm;codecs=opus',
      'audio/ogg;codecs=opus',
      'audio/mp4'
    ];

    let selectedType = null;
    for (const type of mimeTypes) {
      if (MediaRecorder.isTypeSupported(type)) {
        selectedType = type;
        break;
      }
    }

    if (!selectedType) {
      throw new Error('No supported media recording MIME type found');
    }

    // Create recorder with conservative bitrate
    const options = {
      mimeType: selectedType,
      audioBitsPerSecond: 48000 // Using a standard bitrate that works well with transcription services
    };

    const recorder = new MediaRecorder(combinedStream, options);
    refs.mediaRecorderRef.current = recorder;
    refs.audioChunksRef.current = [];

    recorder.ondataavailable = event => {
      if (event.data && event.data.size > 0) {
        refs.audioChunksRef.current.push(event.data);
      }
    };

    recorder.onerror = (e) => {
      setRecordingError(`Recording error: ${e.message || 'Unknown error'}`);
    };

    // Start recording with shorter intervals
    recorder.start(5000); // Capture in 5-second chunks
  } catch (error) {
    setRecordingError(`Failed to start recording: ${error.message}`);
  }
};

/**
 * Stop recording and get the recorded audio blob
 * @param {Object} refs - Object containing mediaRecorderRef, audioChunksRef, and audioContextRef
 * @returns {Promise<Blob|null>} - Promise that resolves to the recorded audio blob or null
 */
export const stopCombinedRecording = async (refs) => {
  return new Promise((resolve, reject) => {
    try {
      if (!refs.mediaRecorderRef.current) {
        resolve(null);
        return;
      }

      // If already inactive, just resolve
      if (refs.mediaRecorderRef.current.state === 'inactive') {
        resolve(null);
        return;
      }

      // Set up stop handler
      refs.mediaRecorderRef.current.onstop = () => {
        try {
          // Get mime type from recorder
          const mimeType = refs.mediaRecorderRef.current.mimeType || 'audio/webm';

          // Create blob from recorded chunks
          const blob = new Blob(refs.audioChunksRef.current, { type: mimeType });

          // Close audio context if it exists
          if (refs.audioContextRef.current && refs.audioContextRef.current.state !== 'closed') {
            refs.audioContextRef.current.close().catch(console.error);
          }

          resolve(blob);
        } catch (err) {
          reject(err);
        }
      };

      // Stop the recorder
      refs.mediaRecorderRef.current.stop();
    } catch (error) {
      reject(error);
    }
  });
};

/**
 * Clean up audio resources
 * @param {MediaStream} remoteStream - The remote audio stream
 * @param {MediaStream} micStream - The local microphone stream
 * @param {Object} refs - Object containing mediaRecorderRef and audioContextRef
 */
export const cleanupAudioResources = (remoteStream, micStream, refs) => {
  // Ensure media recorder is stopped
  if (refs.mediaRecorderRef.current) {
    try {
      if (refs.mediaRecorderRef.current.state !== 'inactive') {
        refs.mediaRecorderRef.current.stop();
      }
    } catch (e) {
      console.error('Error stopping media recorder:', e);
    }
    refs.mediaRecorderRef.current = null;
  }

  // Stop remote stream tracks
  if (remoteStream) {
    remoteStream.getAudioTracks().forEach(track => track.stop());
  }

  // Stop mic stream tracks
  if (micStream) {
    micStream.getAudioTracks().forEach(track => track.stop());
  }

  // Close audio context
  if (refs.audioContextRef.current && refs.audioContextRef.current.state !== 'closed') {
    refs.audioContextRef.current.close().catch(console.error);
    refs.audioContextRef.current = null;
  }
};

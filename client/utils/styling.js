/**
 * Utility functions for styling in the interview application
 */

/**
 * Get editor styles based on dark mode setting
 * @param {boolean} darkMode - Whether dark mode is enabled
 * @returns {Object} - CSS styles for the editor
 */
export const getEditorStyles = (darkMode) => ({
  fontFamily: '"Fira Code", "Fira Mono", monospace',
  fontSize: 16,
  backgroundColor: darkMode ? 'var(--color-dark-base)' : 'var(--color-base)',
  color: darkMode ? 'var(--color-dark-text)' : 'var(--color-ink)',
  minHeight: '100%',
  boxShadow: '0 2px 8px var(--color-shadow)',
  lineHeight: 1.5,
  width: '100%',
  height: '100%',
  borderRadius: '4px',
  ...(darkMode && {
    '.token.operator': { color: 'var(--color-dark-accent)' },
    '.token.punctuation': { color: '#abb2bf' },
    '.token.keyword': { color: 'var(--color-dark-accent)' },
    '.token.string': { color: 'var(--color-nature)' },
    '.token.comment': { color: '#5c6370' },
    '.token.function': { color: 'var(--color-calm)' }
  }),
  ...(!darkMode && {
    '.token.operator': { color: 'var(--color-highlight)' },
    '.token.keyword': { color: 'var(--color-highlight)' },
    '.token.string': { color: 'var(--color-nature)' },
    '.token.comment': { color: '#6a737d' },
    '.token.function': { color: 'var(--color-calm)' }
  })
});

/**
 * Get Ace Editor options
 * @param {boolean} darkMode - Whether dark mode is enabled
 * @returns {Object} - Options for Ace Editor
 */
export const getAceEditorOptions = (darkMode) => ({
  showLineNumbers: true,
  tabSize: 2,
  fontSize: 16,
  fontFamily: '"Fira Code", "Fira Mono", monospace',
  highlightActiveLine: true,
  showPrintMargin: false,
  scrollMargin: [10, 10, 10, 10],
  animatedScroll: true,
});

/**
 * Format time in seconds to MM:SS format
 * @param {number} timeInSeconds - Time in seconds
 * @returns {string} - Formatted time string (MM:SS)
 */
export const formatTimeLeft = (timeInSeconds) => {
  const minutes = Math.floor(timeInSeconds / 60);
  const seconds = timeInSeconds % 60;
  return `${minutes}:${seconds.toString().padStart(2, '0')}`;
};

/**
 * Get color for score based on percentage
 * @param {number} score - The score value
 * @param {number} maxScore - The maximum possible score
 * @returns {string} - CSS class for the score color
 */
export const getScoreColor = (score, maxScore) => {
  const percentage = (score / maxScore) * 100;
  if (percentage >= 80) return 'text-green-600 dark:text-green-400';
  if (percentage >= 60) return 'text-blue-600 dark:text-blue-400';
  if (percentage >= 40) return 'text-yellow-600 dark:text-yellow-400';
  return 'text-red-600 dark:text-red-400';
};

/**
 * Get emoji for score based on percentage
 * @param {number} score - The score value
 * @param {number} maxScore - The maximum possible score
 * @returns {string} - Emoji representing the score
 */
export const getScoreEmoji = (score, maxScore) => {
  const percentage = (score / maxScore) * 100;
  if (percentage >= 80) return '🎯';
  if (percentage >= 60) return '🎨';
  if (percentage >= 40) return '⚠️';
  return '💥';
};

/**
 * Format difficulty level to be more readable
 * @param {string} difficulty - The difficulty level (easy, medium, hard)
 * @returns {string} - Formatted difficulty string
 */
export const formatDifficulty = (difficulty) => {
  if (!difficulty || difficulty === 'unknown') return 'Unknown';

  const difficultyMap = {
    'easy': 'Novice Path',
    'medium': 'Warrior Path',
    'hard': 'Master Path'
  };

  return difficultyMap[difficulty] || difficulty.charAt(0).toUpperCase() + difficulty.slice(1);
};

/**
 * Format timestamp to a readable date
 * @param {Date|Object} timestamp - The timestamp to format
 * @returns {string} - Formatted date string
 */
export const formatDate = (timestamp) => {
  if (!timestamp) return 'Unknown date';

  // If it's a Firestore timestamp, convert to JS Date
  const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);

  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date);
};

/**
 * Configuration for sub-score categories
 */
export const subScoreConfig = {
  correctness: { max: 25, label: 'Correctness' },
  time_space_complexity: { max: 15, label: 'Time/Space Complexity' },
  code_structure: { max: 15, label: 'Code Structure' },
  data_structures: { max: 10, label: 'Data Structures' },
  algorithm_design: { max: 15, label: 'Algorithm Design' },
  debugging_testing: { max: 10, label: 'Debugging & Testing' },
  communication: { max: 5, label: 'Communication' },
  iteration_improvement: { max: 5, label: 'Iteration & Improvement' }
};

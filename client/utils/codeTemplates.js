/**
 * Code templates for different programming languages
 */

export const codeTemplates = {
  javascript: `// JavaScript Solution
function solution(input) {
  // Your code here

  return result;
}

// Example usage
const result = solution([1, 2, 3]);
console.log(result);
`,

  typescript: `// TypeScript Solution
function solution(input: number[]): any {
  // Your code here

  return result;
}

// Example usage
const result = solution([1, 2, 3]);
console.log(result);
`,

  python: `# Python Solution
def solution(input):
    # Your code here

    return result

# Example usage
result = solution([1, 2, 3])
print(result)
`,

  java: `// Java Solution
public class Solution {
    public static void main(String[] args) {
        // Example usage
        int[] input = {1, 2, 3};
        System.out.println(solution(input));
    }

    public static Object solution(int[] input) {
        // Your code here

        return null; // Replace with your result
    }
}
`,

  go: `// Go Solution
package main

import "fmt"

func solution(input []int) interface{} {
    // Your code here

    return nil // Replace with your result
}

func main() {
    // Example usage
    input := []int{1, 2, 3}
    result := solution(input)
    fmt.Println(result)
}
`,

  ruby: `# Ruby Solution
def solution(input)
  # Your code here

  return result
end

# Example usage
result = solution([1, 2, 3])
puts result
`,

  cpp: `// C++ Solution
#include <iostream>
#include <vector>

using namespace std;

// Solution function
auto solution(const vector<int>& input) {
    // Your code here

    return 0; // Replace with your result
}

int main() {
    // Example usage
    vector<int> input = {1, 2, 3};
    auto result = solution(input);
    cout << result << endl;

    return 0;
}
`,

  csharp: `// C# Solution
using System;
using System.Collections.Generic;

class Solution {
    static void Main() {
        // Example usage
        int[] input = {1, 2, 3};
        var result = Solve(input);
        Console.WriteLine(result);
    }

    static object Solve(int[] input) {
        // Your code here

        return null; // Replace with your result
    }
}
`,

  rust: `// Rust Solution
fn main() {
    // Example usage
    let input = vec![1, 2, 3];
    let result = solution(&input);
    println!("{:?}", result);
}

fn solution(input: &[i32]) -> Option<i32> {
    // Your code here

    None // Replace with your result
}
`,

  swift: `// Swift Solution
func solution(input: [Int]) -> Any {
    // Your code here

    return 0 // Replace with your result
}

// Example usage
let input = [1, 2, 3]
let result = solution(input: input)
print(result)
`
};

/**
 * Get the appropriate Ace Editor mode for a language
 * @param {string} language - The language identifier
 * @returns {string} - The Ace Editor mode name
 */
export const getEditorMode = (language) => {
  const modeMap = {
    javascript: 'javascript',
    typescript: 'typescript',
    python: 'python',
    java: 'java',
    go: 'golang',
    ruby: 'ruby',
    cpp: 'c_cpp',
    csharp: 'csharp',
    rust: 'rust',
    swift: 'swift'
  };

  return modeMap[language] || 'javascript';
};

/**
 * Get the appropriate Ace Editor theme based on dark mode
 * @param {boolean} darkMode - Whether dark mode is enabled
 * @returns {string} - The Ace Editor theme name
 */
export const getEditorTheme = (darkMode) => {
  return darkMode ? 'monokai' : 'github';
};

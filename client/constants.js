export const questionsData = {
  "easy": [
    {
      "id": 1,
      "title": "Two Sum",
      "description": "Given an array of integers and a target sum, find two numbers in the array that add up to the target. Return their indices. Assume there is exactly one solution.",
      "example": "Input: nums = [2, 7, 11, 15], target = 9 -> Output: [0, 1]"
    },
    {
      "id": 2,
      "title": "Reverse String",
      "description": "Write a function that reverses a string. The input string is given as an array of characters. Do this in-place with O(1) extra memory.",
      "example": "Input: ['h','e','l','l','o'] -> Output: ['o','l','l','e','h']"
    },
    {
      "id": 3,
      "title": "Palindrome Check",
      "description": "Determine whether a given string is a palindrome, considering only alphanumeric characters and ignoring case.",
      "example": "Input: 'A man, a plan, a canal: Panama' -> Output: true"
    },
    {
      "id": 4,
      "title": "Valid Anagram",
      "description": "Given two strings, determine if they are anagrams of each other. Two strings are anagrams if they contain the same characters with the same frequency.",
      "example": "Input: s = 'anagram', t = 'nagaram' -> Output: true"
    },
    {
      "id": 5,
      "title": "Array Rotation",
      "description": "Given an array of integers and a non-negative integer k, rotate the array to the right by k steps. Each element should move k positions to the right, and elements that exceed the array bounds should wrap around to the start. Consider edge cases such as k being 0, k equal to the length of the array, or k larger than the array length.",
      "example": "Input: array = [1, 2, 3, 4, 5], k = 2 -> Output: [4, 5, 1, 2, 3]"
    },
    {
      "id": 6,
      "title": "Binary Search",
      "description": "Implement binary search on a sorted array of integers. Return the index of the target value if found, otherwise return -1. Your solution should efficiently handle cases with large arrays and properly account for edge cases such as empty arrays or targets outside the array range.",
      "example": "Input: array = [1, 3, 5, 7, 9], target = 5 -> Output: 2"
    },
    {
      "id": 7,
      "title": "Unique Elements Extraction",
      "description": "Given an array of integers, extract and return a new array containing only the unique elements (no duplicates) while preserving the order of their first occurrence. Ensure your solution handles arrays of various lengths and content.",
      "example": "Input: array = [1, 2, 2, 3, 4, 4] -> Output: [1, 2, 3, 4]"
    },
    {
      "id": 8,
      "title": "String Addition",
      "description": "Given two non-negative numbers represented as strings, compute and return their sum as a string. The solution should handle cases where the numbers have different lengths and manage potential carry-over between digits.",
      "example": "Input: num1 = \"123\", num2 = \"456\" -> Output: \"579\""
    },
    {
      "id": 9,
      "title": "First Occurrence Finder",
      "description": "In a sorted array, find the first occurrence of a target value. If the target is found multiple times, return the index of its first appearance; if not found, return -1. Your solution should be optimized for performance on large arrays.",
      "example": "Input: array = [1, 2, 2, 3, 4], target = 2 -> Output: 1"
    },
    {
      "id": 10,
      "title": "Fibonacci Sequence Generator",
      "description": "Generate the first n numbers in the Fibonacci sequence, where each number is the sum of the two preceding ones, starting from 0 and 1. Ensure the algorithm efficiently handles cases when n is small as well as larger values.",
      "example": "Input: n = 5 -> Output: [0, 1, 1, 2, 3]"
    },
    {
      "id": 31,
      "title": "Maximum Subarray",
      "description": "Find the contiguous subarray with the largest sum and return the sum. Use Kadane's algorithm for an optimal O(n) solution.",
      "example": "Input: [-2,1,-3,4,-1,2,1,-5,4] -> Output: 6 (subarray [4,-1,2,1])"
    },
    {
      "id": 32,
      "title": "Remove Duplicates from Sorted Array",
      "description": "Given a sorted array, remove duplicates in-place and return the new length. Don't allocate extra space for another array.",
      "example": "Input: [1,1,2] -> Output: 2 (array becomes [1,2,...])"
    },
    {
      "id": 33,
      "title": "Plus One",
      "description": "Given a non-empty array representing a non-negative integer, add one to the integer. The digits are stored such that the most significant digit is at the head of the list.",
      "example": "Input: [1,2,3] -> Output: [1,2,4]"
    },
    {
      "id": 34,
      "title": "Move Zeroes",
      "description": "Given an array, move all 0's to the end while maintaining the relative order of non-zero elements. Do this in-place without making a copy of the array.",
      "example": "Input: [0,1,0,3,12] -> Output: [1,3,12,0,0]"
    },
    {
      "id": 35,
      "title": "Intersection of Two Arrays",
      "description": "Given two arrays, compute their intersection. Each element in the result should appear as many times as it shows in both arrays.",
      "example": "Input: nums1 = [1,2,2,1], nums2 = [2,2] -> Output: [2,2]"
    },
    {
      "id": 36,
      "title": "Best Time to Buy and Sell Stock",
      "description": "You have an array of prices where prices[i] is the price of a given stock on day i. Find the maximum profit you can achieve from one transaction.",
      "example": "Input: [7,1,5,3,6,4] -> Output: 5 (buy at 1, sell at 6)"
    },
    {
      "id": 37,
      "title": "Valid Parentheses",
      "description": "Given a string containing just '(', ')', '{', '}', '[' and ']', determine if the input string is valid. Open brackets must be closed by the same type of brackets in the correct order.",
      "example": "Input: '()[]{}' -> Output: true"
    },
    {
      "id": 38,
      "title": "Merge Two Sorted Lists",
      "description": "Merge two sorted linked lists and return it as a new sorted list. The new list should be made by splicing together the nodes of the first two lists.",
      "example": "Input: l1 = [1,2,4], l2 = [1,3,4] -> Output: [1,1,2,3,4,4]"
    },
    {
      "id": 39,
      "title": "Remove Element",
      "description": "Given an array and a value, remove all instances of that value in-place and return the new length. Don't allocate extra space for another array.",
      "example": "Input: nums = [3,2,2,3], val = 3 -> Output: 2 (array becomes [2,2,...])"
    },
    {
      "id": 40,
      "title": "Implement strStr()",
      "description": "Return the index of the first occurrence of needle in haystack, or -1 if needle is not part of haystack. Similar to indexOf() function.",
      "example": "Input: haystack = 'hello', needle = 'll' -> Output: 2"
    },
    {
      "id": 41,
      "title": "Length of Last Word",
      "description": "Given a string consisting of words and spaces, return the length of the last word in the string. A word is a maximal substring consisting of non-space characters only.",
      "example": "Input: 'Hello World' -> Output: 5"
    },
    {
      "id": 42,
      "title": "Climbing Stairs",
      "description": "You are climbing a staircase with n steps. Each time you can either climb 1 or 2 steps. In how many distinct ways can you climb to the top?",
      "example": "Input: n = 3 -> Output: 3 (ways: 1+1+1, 1+2, 2+1)"
    },
    {
      "id": 43,
      "title": "Same Tree",
      "description": "Given two binary trees, write a function to check if they are the same or not. Two binary trees are considered the same if they are structurally identical and the nodes have the same value.",
      "example": "Input: p = [1,2,3], q = [1,2,3] -> Output: true"
    },
    {
      "id": 44,
      "title": "Symmetric Tree",
      "description": "Given a binary tree, check whether it is a mirror of itself (i.e., symmetric around its center).",
      "example": "Input: [1,2,2,3,4,4,3] -> Output: true"
    },
    {
      "id": 45,
      "title": "Maximum Depth of Binary Tree",
      "description": "Given a binary tree, find its maximum depth. The maximum depth is the number of nodes along the longest path from the root node down to the farthest leaf node.",
      "example": "Input: [3,9,20,null,null,15,7] -> Output: 3"
    }
  ],
  "medium": [
    {
      "id": 11,
      "title": "Contiguous Subarray Sum",
      "description": "Given an array of integers and a target sum, find a contiguous subarray that sums exactly to the target. If multiple solutions exist, return any one of them. Consider handling scenarios where the array contains negative numbers, and test for edge cases such as an empty array.",
      "example": "Input: array = [1, 2, 3, 4, 5], target = 9 -> Output: [2, 3, 4]"
    },
    {
      "id": 12,
      "title": "Longest Palindromic Substring",
      "description": "Given a string, find the longest substring that forms a palindrome. Your solution should handle both even and odd length palindromes, and consider cases where multiple palindromic substrings of the same length exist.",
      "example": "Input: \"babad\" -> Output: \"bab\" (or \"aba\")"
    },
    {
      "id": 13,
      "title": "Merge Two Sorted Arrays",
      "description": "Given two sorted arrays of integers, merge them into one sorted array. The final output should maintain sorted order without using extra space beyond what is necessary for the merged array.",
      "example": "Input: array1 = [1, 3, 5], array2 = [2, 4, 6] -> Output: [1, 2, 3, 4, 5, 6]"
    },
    {
      "id": 14,
      "title": "Matrix Rotation",
      "description": "Given an n x n matrix, rotate the matrix by 90 degrees clockwise in-place. Ensure that the algorithm efficiently processes the matrix without using extra space for another matrix.",
      "example": "Input: matrix = [[1, 2], [3, 4]] -> Output: [[3, 1], [4, 2]]"
    },
    {
      "id": 15,
      "title": "Balanced Parentheses Validator",
      "description": "Given a string containing various types of brackets (e.g., '()', '{}', '[]'), determine if the string is valid. A valid string must have all open brackets closed by the same type of brackets in the correct order.",
      "example": "Input: \"([]){}\" -> Output: true"
    },
    {
      "id": 16,
      "title": "String Permutations Generator",
      "description": "Generate all possible permutations of a given string. The solution should account for strings with duplicate characters and return all unique permutation results.",
      "example": "Input: \"abc\" -> Output: [\"abc\", \"acb\", \"bac\", \"bca\", \"cab\", \"cba\"]"
    },
    {
      "id": 17,
      "title": "Cycle Detection in Linked List",
      "description": "Given the head of a linked list, determine whether the linked list contains a cycle. Your solution should use an efficient algorithm such as Floyd's Cycle Detection algorithm.",
      "example": "Input: head of a linked list with a cycle -> Output: true; without a cycle -> Output: false"
    },
    {
      "id": 18,
      "title": "Merge Overlapping Intervals",
      "description": "Given a collection of intervals, merge all overlapping intervals into one and return the resulting array. The solution should efficiently handle intervals that are not initially sorted.",
      "example": "Input: intervals = [[1, 3], [2, 6], [8, 10], [15, 18]] -> Output: [[1, 6], [8, 10], [15, 18]]"
    },
    {
      "id": 19,
      "title": "K Closest Elements Finder",
      "description": "Given a sorted array, a target value, and an integer k, find the k elements in the array that are closest to the target. If two numbers are equally close, prioritize the smaller number.",
      "example": "Input: array = [1, 2, 3, 4, 5], k = 4, target = 3 -> Output: [1, 2, 3, 4]"
    },
    {
      "id": 20,
      "title": "Word Break Problem",
      "description": "Given a non-empty string and a dictionary of words, determine if the string can be segmented into a space-separated sequence of one or more dictionary words. Consider dynamic programming solutions for efficiency.",
      "example": "Input: s = \"leetcode\", wordDict = [\"leet\", \"code\"] -> Output: true"
    },
    {
      "id": 46,
      "title": "3Sum",
      "description": "Given an array of integers, find all unique triplets in the array that sum to zero. The solution set must not contain duplicate triplets.",
      "example": "Input: [-1,0,1,2,-1,-4] -> Output: [[-1,-1,2],[-1,0,1]]"
    },
    {
      "id": 47,
      "title": "Container With Most Water",
      "description": "Given n non-negative integers representing an elevation map, find two lines that together with the x-axis forms a container that holds the most water.",
      "example": "Input: [1,8,6,2,5,4,8,3,7] -> Output: 49"
    },
    {
      "id": 48,
      "title": "Generate Parentheses",
      "description": "Given n pairs of parentheses, write a function to generate all combinations of well-formed parentheses.",
      "example": "Input: n = 3 -> Output: ['((()))', '(()())', '(())()', '()(())', '()()()']"
    },
    {
      "id": 49,
      "title": "Remove Nth Node From End of List",
      "description": "Given the head of a linked list, remove the nth node from the end of the list and return its head. Try to do this in one pass.",
      "example": "Input: head = [1,2,3,4,5], n = 2 -> Output: [1,2,3,5]"
    },
    {
      "id": 50,
      "title": "Swap Nodes in Pairs",
      "description": "Given a linked list, swap every two adjacent nodes and return its head. You may not modify the values in the list's nodes, only nodes itself may be changed.",
      "example": "Input: [1,2,3,4] -> Output: [2,1,4,3]"
    },
    {
      "id": 51,
      "title": "Search in Rotated Sorted Array",
      "description": "You are given an integer array sorted in ascending order, and an integer target. Suppose the array is rotated at some pivot. Search for target and return its index, or -1 if not found.",
      "example": "Input: nums = [4,5,6,7,0,1,2], target = 0 -> Output: 4"
    },
    {
      "id": 52,
      "title": "Find First and Last Position",
      "description": "Given an array of integers sorted in ascending order, find the starting and ending position of a given target value. If target is not found, return [-1, -1].",
      "example": "Input: nums = [5,7,7,8,8,10], target = 8 -> Output: [3,4]"
    },
    {
      "id": 53,
      "title": "Combination Sum",
      "description": "Given a set of candidate numbers and a target number, find all unique combinations where the candidate numbers sum to target. The same repeated number may be chosen unlimited times.",
      "example": "Input: candidates = [2,3,6,7], target = 7 -> Output: [[2,2,3],[7]]"
    },
    {
      "id": 54,
      "title": "Permutations",
      "description": "Given an array of distinct integers, return all possible permutations. You can return the answer in any order.",
      "example": "Input: [1,2,3] -> Output: [[1,2,3],[1,3,2],[2,1,3],[2,3,1],[3,1,2],[3,2,1]]"
    },
    {
      "id": 55,
      "title": "Group Anagrams",
      "description": "Given an array of strings, group the anagrams together. You can return the answer in any order.",
      "example": "Input: ['eat','tea','tan','ate','nat','bat'] -> Output: [['bat'],['nat','tan'],['ate','eat','tea']]"
    },
    {
      "id": 56,
      "title": "Spiral Matrix",
      "description": "Given an m x n matrix, return all elements of the matrix in spiral order (clockwise from outside to inside).",
      "example": "Input: [[1,2,3],[4,5,6],[7,8,9]] -> Output: [1,2,3,6,9,8,7,4,5]"
    },
    {
      "id": 57,
      "title": "Jump Game",
      "description": "Given an array of non-negative integers, you are initially positioned at the first index. Each element represents your maximum jump length. Determine if you can reach the last index.",
      "example": "Input: [2,3,1,1,4] -> Output: true"
    },
    {
      "id": 58,
      "title": "Unique Paths",
      "description": "A robot is located at the top-left corner of an m x n grid. The robot can only move either down or right. How many possible unique paths are there to reach the bottom-right corner?",
      "example": "Input: m = 3, n = 7 -> Output: 28"
    },
    {
      "id": 59,
      "title": "Minimum Path Sum",
      "description": "Given an m x n grid filled with non-negative numbers, find a path from top left to bottom right that minimizes the sum of all numbers along its path. You can only move down or right.",
      "example": "Input: [[1,3,1],[1,5,1],[4,2,1]] -> Output: 7 (path: 1→3→1→1→1)"
    },
    {
      "id": 60,
      "title": "Set Matrix Zeroes",
      "description": "Given an m x n matrix, if an element is 0, set its entire row and column to 0. Do it in-place using constant extra space.",
      "example": "Input: [[1,1,1],[1,0,1],[1,1,1]] -> Output: [[1,0,1],[0,0,0],[1,0,1]]"
    },
    {
      "id": 61,
      "title": "Search a 2D Matrix",
      "description": "Write an efficient algorithm that searches for a value in an m x n matrix. The matrix has properties: integers in each row are sorted left to right, first integer of each row is greater than the last integer of the previous row.",
      "example": "Input: matrix = [[1,4,7,11],[2,5,8,12],[3,6,9,16]], target = 5 -> Output: true"
    },
    {
      "id": 62,
      "title": "Sort Colors",
      "description": "Given an array with n objects colored red, white, or blue (represented by 0, 1, 2), sort them in-place so that objects of the same color are adjacent. Use one-pass algorithm with constant space.",
      "example": "Input: [2,0,2,1,1,0] -> Output: [0,0,1,1,2,2]"
    },
    {
      "id": 63,
      "title": "Subsets",
      "description": "Given an integer array of unique elements, return all possible subsets (the power set). The solution set must not contain duplicate subsets.",
      "example": "Input: [1,2,3] -> Output: [[],[1],[2],[1,2],[3],[1,3],[2,3],[1,2,3]]"
    },
    {
      "id": 64,
      "title": "Word Search",
      "description": "Given an m x n grid of characters and a string word, return true if word exists in the grid. The word can be constructed from letters sequentially adjacent cells (not diagonally).",
      "example": "Input: board = [['A','B','C','E'],['S','F','C','S'],['A','D','E','E']], word = 'ABCCED' -> Output: true"
    },
    {
      "id": 65,
      "title": "Remove Duplicates from Sorted Array II",
      "description": "Given a sorted array, remove the duplicates in-place such that duplicates appeared at most twice and return the new length. Do not allocate extra space.",
      "example": "Input: [1,1,1,2,2,3] -> Output: 5 (array becomes [1,1,2,2,3,...])"
    }
  ],
  "hard": [
    {
      "id": 21,
      "title": "Longest Increasing Subsequence",
      "description": "Given an unsorted array of integers, find the length of the longest subsequence such that every element in the subsequence is greater than the preceding element. Consider using dynamic programming for an efficient solution.",
      "example": "Input: [10, 9, 2, 5, 3, 7, 101, 18] -> Output: 4 (e.g., subsequence [2, 3, 7, 101])"
    },
    {
      "id": 22,
      "title": "Regular Expression Matching",
      "description": "Implement regular expression matching with support for '.' and '*'. The dot '.' should match any single character, and the star '*' matches zero or more of the preceding element. Your algorithm should correctly handle complex patterns.",
      "example": "Input: s = \"aab\", p = \"c*a*b\" -> Output: true"
    },
    {
      "id": 23,
      "title": "N-Queens Problem",
      "description": "Place n queens on an n×n chessboard such that no two queens threaten each other. Return all distinct board configurations where the queens are safely placed. Solutions should consider the constraints on rows, columns, and diagonals.",
      "example": "Input: n = 4 -> Output: A list of valid board configurations (each configuration represented as an array of strings)"
    },
    {
      "id": 24,
      "title": "Sudoku Solver",
      "description": "Write a program to solve a Sudoku puzzle by filling the empty cells. The input will be a partially filled 9x9 board, and the solution must obey Sudoku rules. Consider using backtracking for an efficient approach.",
      "example": "Input: A 9x9 board with zeros representing empty cells -> Output: A completely filled 9x9 board that is a valid Sudoku solution"
    },
    {
      "id": 25,
      "title": "Trapping Rain Water",
      "description": "Given an array representing the elevation map where the width of each bar is 1, compute how much water it can trap after raining. The solution should efficiently handle large arrays and varying elevation patterns.",
      "example": "Input: [0, 1, 0, 2, 1, 0, 1, 3, 2, 1, 2, 1] -> Output: 6"
    },
    {
      "id": 26,
      "title": "Edit Distance",
      "description": "Given two strings, determine the minimum number of operations (insertions, deletions, substitutions) required to convert one string into the other. Use dynamic programming to build an efficient solution.",
      "example": "Input: word1 = \"horse\", word2 = \"ros\" -> Output: 3"
    },
    {
      "id": 27,
      "title": "Maximum Flow Problem",
      "description": "Given a directed graph where each edge has a capacity, determine the maximum flow from a source node to a sink node. Use algorithms such as Ford-Fulkerson or Edmonds-Karp to compute the solution.",
      "example": "Input: Graph represented as a set of nodes and capacities on edges -> Output: Maximum possible flow value from source to sink"
    },
    {
      "id": 28,
      "title": "Data Stream Median",
      "description": "Design a data structure that supports the insertion of numbers from a data stream and can return the median of all inserted numbers in O(log n) time per insertion. Ensure that your implementation correctly handles both even and odd numbers of total elements.",
      "example": "Input: A sequence of numbers inserted one by one -> Output: Median after each insertion"
    },
    {
      "id": 29,
      "title": "Word Ladder II",
      "description": "Given a begin word, an end word, and a dictionary of words, find all the shortest transformation sequences from the begin word to the end word, where each transformation changes only one character and must result in a valid word from the dictionary. Use breadth-first search to ensure shortest paths.",
      "example": "Input: beginWord = \"hit\", endWord = \"cog\", wordList = [\"hot\",\"dot\",\"dog\",\"lot\",\"log\",\"cog\"] -> Output: List of shortest transformation sequences, e.g., [[\"hit\",\"hot\",\"dot\",\"dog\",\"cog\"], [\"hit\",\"hot\",\"lot\",\"log\",\"cog\"]]"
    },
    {
      "id": 30,
      "title": "Alien Dictionary",
      "description": "Given a list of words sorted in lexicographical order according to an unknown alien language, derive the order of characters in that language. Consider building a graph to represent the character precedence relationships and perform a topological sort.",
      "example": "Input: [\"wrt\", \"wrf\", \"er\", \"ett\", \"rftt\"] -> Output: A string that represents the order of characters, such as \"wertf\""
    },
    {
      "id": 66,
      "title": "Median of Two Sorted Arrays",
      "description": "Given two sorted arrays, find the median of the two sorted arrays. The overall run time complexity should be O(log (m+n)) where m and n are the sizes of the arrays.",
      "example": "Input: nums1 = [1,3], nums2 = [2] -> Output: 2.0"
    },
    {
      "id": 67,
      "title": "Longest Valid Parentheses",
      "description": "Given a string containing just '(' and ')', find the length of the longest valid (well-formed) parentheses substring.",
      "example": "Input: ')()())' -> Output: 4 (substring '()()')"
    },
    {
      "id": 68,
      "title": "First Missing Positive",
      "description": "Given an unsorted integer array, find the smallest missing positive integer. Your algorithm should run in O(n) time and use constant extra space.",
      "example": "Input: [1,2,0] -> Output: 3"
    },
    {
      "id": 69,
      "title": "Wildcard Matching",
      "description": "Given an input string and a pattern, implement wildcard pattern matching with support for '?' and '*' where '?' matches any single character and '*' matches any sequence of characters.",
      "example": "Input: s = 'adceb', p = '*a*b*' -> Output: true"
    },
    {
      "id": 70,
      "title": "Jump Game II",
      "description": "Given an array of non-negative integers, you are initially positioned at the first index. Each element represents your maximum jump length. Your goal is to reach the last index in the minimum number of jumps.",
      "example": "Input: [2,3,1,1,4] -> Output: 2 (jump 1 step from index 0 to 1, then 3 steps to the last index)"
    },
    {
      "id": 71,
      "title": "Largest Rectangle in Histogram",
      "description": "Given an array of integers representing the height of bars in a histogram, find the area of the largest rectangle that can be formed within the histogram.",
      "example": "Input: [2,1,5,6,2,3] -> Output: 10 (rectangle with height 5 and width 2)"
    },
    {
      "id": 72,
      "title": "Maximal Rectangle",
      "description": "Given a 2D binary matrix filled with 0's and 1's, find the largest rectangle containing only 1's and return its area.",
      "example": "Input: [['1','0','1','0','0'],['1','0','1','1','1'],['1','1','1','1','1'],['1','0','0','1','0']] -> Output: 6"
    },
    {
      "id": 73,
      "title": "Scramble String",
      "description": "Given two strings s1 and s2 of the same length, determine if s2 is a scrambled string of s1. A string can be scrambled by recursively dividing it and swapping the two parts.",
      "example": "Input: s1 = 'great', s2 = 'rgeat' -> Output: true"
    },
    {
      "id": 74,
      "title": "Merge k Sorted Lists",
      "description": "You are given an array of k linked-lists, each linked-list is sorted in ascending order. Merge all the linked-lists into one sorted linked-list and return it.",
      "example": "Input: [[1,4,5],[1,3,4],[2,6]] -> Output: [1,1,2,3,4,4,5,6]"
    },
    {
      "id": 75,
      "title": "Reverse Nodes in k-Group",
      "description": "Given a linked list, reverse the nodes of the list k at a time and return the modified list. If the number of nodes is not a multiple of k, leave the remaining nodes as they are.",
      "example": "Input: head = [1,2,3,4,5], k = 2 -> Output: [2,1,4,3,5]"
    },
    {
      "id": 76,
      "title": "Minimum Window Substring",
      "description": "Given two strings s and t, return the minimum window in s which will contain all the characters in t. If there is no such window, return an empty string.",
      "example": "Input: s = 'ADOBECODEBANC', t = 'ABC' -> Output: 'BANC'"
    },
    {
      "id": 77,
      "title": "Sliding Window Maximum",
      "description": "Given an array and a sliding window of size k, find the maximum value in each window as it slides from left to right.",
      "example": "Input: nums = [1,3,-1,-3,5,3,6,7], k = 3 -> Output: [3,3,5,5,6,7]"
    },
    {
      "id": 78,
      "title": "Basic Calculator",
      "description": "Implement a basic calculator to evaluate a simple expression string containing non-negative integers, '+', '-', '*', '/', and spaces. The integer division should truncate toward zero.",
      "example": "Input: s = '3+2*2' -> Output: 7"
    },
    {
      "id": 79,
      "title": "Word Break II",
      "description": "Given a string s and a dictionary of strings wordDict, add spaces in s to construct a sentence where each word is a valid dictionary word. Return all such possible sentences in any order.",
      "example": "Input: s = 'catsanddog', wordDict = ['cat','cats','and','sand','dog'] -> Output: ['cats and dog','cat sand dog']"
    },
    {
      "id": 80,
      "title": "LRU Cache",
      "description": "Design a data structure that follows the constraints of a Least Recently Used (LRU) cache. Implement get(key) and put(key, value) methods that both run in O(1) average time complexity.",
      "example": "Input: LRUCache(2); put(1,1); put(2,2); get(1); put(3,3); get(2); put(4,4); get(1); get(3); get(4) -> Output: [null,null,null,1,null,-1,null,-1,3,4]"
    },
    {
      "id": 81,
      "title": "Binary Tree Maximum Path Sum",
      "description": "A path in a binary tree is a sequence of nodes where each pair of adjacent nodes has an edge connecting them. Find the maximum sum of any non-empty path.",
      "example": "Input: root = [1,2,3] -> Output: 6 (path: 2 -> 1 -> 3)"
    },
    {
      "id": 82,
      "title": "Best Time to Buy and Sell Stock III",
      "description": "You can complete at most two transactions (buy and sell twice). Find the maximum profit you can achieve. You may not engage in multiple transactions simultaneously.",
      "example": "Input: prices = [3,3,5,0,0,3,1,4] -> Output: 6 (buy at 0, sell at 3, buy at 1, sell at 4)"
    },
    {
      "id": 83,
      "title": "Word Ladder",
      "description": "Given two words (beginWord and endWord) and a dictionary, find the length of shortest transformation sequence from beginWord to endWord, changing only one letter at a time.",
      "example": "Input: beginWord = 'hit', endWord = 'cog', wordList = ['hot','dot','dog','lot','log','cog'] -> Output: 5"
    },
    {
      "id": 84,
      "title": "Palindrome Partitioning II",
      "description": "Given a string s, partition s such that every substring of the partition is a palindrome. Return the minimum cuts needed for a palindrome partitioning of s.",
      "example": "Input: s = 'aab' -> Output: 1 (palindrome partitioning ['aa','b'])"
    },
    {
      "id": 85,
      "title": "Best Time to Buy and Sell Stock IV",
      "description": "You can complete at most k transactions. Find the maximum profit you can achieve. Each transaction consists of buying and then selling one share of the stock.",
      "example": "Input: k = 2, prices = [2,4,1] -> Output: 2 (buy at 2, sell at 4)"
    },
    {
      "id": 86,
      "title": "Burst Balloons",
      "description": "Given n balloons with numbers on them, burst all balloons. If you burst balloon i, you get nums[left] * nums[i] * nums[right] coins. Find the maximum coins you can collect.",
      "example": "Input: nums = [3,1,5,8] -> Output: 167"
    },
    {
      "id": 87,
      "title": "Remove Invalid Parentheses",
      "description": "Given a string s that contains parentheses and letters, remove the minimum number of invalid parentheses to make the input string valid. Return all possible results.",
      "example": "Input: s = '()())()' -> Output: ['()()()', '(())()']"
    },
    {
      "id": 88,
      "title": "Russian Doll Envelopes",
      "description": "You have envelopes with widths and heights. One envelope can fit into another if both width and height are smaller. Find the maximum number of envelopes you can Russian doll.",
      "example": "Input: envelopes = [[5,4],[6,4],[6,7],[2,3]] -> Output: 3"
    },
    {
      "id": 89,
      "title": "Count of Smaller Numbers After Self",
      "description": "Given an integer array nums, return an integer array counts where counts[i] is the number of smaller elements to the right of nums[i].",
      "example": "Input: nums = [5,2,6,1] -> Output: [2,1,1,0]"
    },
    {
      "id": 90,
      "title": "Create Maximum Number",
      "description": "Given two arrays of length m and n, create the maximum number of length k <= m + n from digits of the two arrays. The relative order of digits from the same array must be preserved.",
      "example": "Input: nums1 = [3,4,6,5], nums2 = [9,1,2,5,8,3], k = 5 -> Output: [9,8,6,5,3]"
    },
    {
      "id": 91,
      "title": "Palindromic Substrings",
      "description": "Given a string s, return the number of palindromic substrings in it. A string is a palindrome when it reads the same backward as forward.",
      "example": "Input: s = 'abc' -> Output: 3 (substrings: 'a', 'b', 'c')"
    },
    {
      "id": 92,
      "title": "Text Justification",
      "description": "Given an array of words and a width maxWidth, format the text such that each line has exactly maxWidth characters and is fully justified.",
      "example": "Input: words = ['This', 'is', 'an', 'example'], maxWidth = 16 -> Output: ['This    is    an', 'example         ']"
    },
    {
      "id": 93,
      "title": "Integer to English Words",
      "description": "Convert a non-negative integer num to its English words representation.",
      "example": "Input: num = 123 -> Output: 'One Hundred Twenty Three'"
    },
    {
      "id": 94,
      "title": "Expression Add Operators",
      "description": "Given a string num that contains only digits and an integer target, return all possibilities to add the binary operators '+', '-', or '*' between digits to evaluate to the target value.",
      "example": "Input: num = '123', target = 6 -> Output: ['1+2+3', '1*2*3']"
    },
    {
      "id": 95,
      "title": "Shortest Distance from All Buildings",
      "description": "You are given an m x n grid grid of values 0, 1, or 2, where 0 represents empty land, 1 represents a building, and 2 represents an obstacle. Find the shortest distance from all buildings to build a house.",
      "example": "Input: grid = [[1,0,2,0,1],[0,0,0,0,0],[0,0,1,0,0]] -> Output: 7"
    },
    {
      "id": 96,
      "title": "Valid Number",
      "description": "Given a string s, return whether s is a valid number. A valid number can be split into these components: sign, integer, decimal point, fractional, exponent indicator, sign, integer.",
      "example": "Input: s = '0' -> Output: true"
    },
    {
      "id": 97,
      "title": "Reconstruct Itinerary",
      "description": "Given a list of airline tickets, reconstruct the itinerary in order. All tickets belong to a man who departs from 'JFK', thus the itinerary must begin with 'JFK'.",
      "example": "Input: tickets = [['MUC','LHR'],['JFK','MUC'],['SFO','SJC'],['LHR','SFO']] -> Output: ['JFK','MUC','LHR','SFO','SJC']"
    },
    {
      "id": 98,
      "title": "Serialize and Deserialize Binary Tree",
      "description": "Design an algorithm to serialize and deserialize a binary tree. Serialization is converting a tree to a string, deserialization is reconstructing the tree from the string.",
      "example": "Input: root = [1,2,3,null,null,4,5] -> Output: Serialized string that can be deserialized back to the original tree"
    },
    {
      "id": 99,
      "title": "Smallest Range Covering Elements from K Lists",
      "description": "You have k lists of sorted integers. Find the smallest range that includes at least one number from each of the k lists.",
      "example": "Input: nums = [[4,10,15,24,26],[0,9,12,20],[5,18,22,30]] -> Output: [20,24]"
    },
    {
      "id": 100,
      "title": "Design Search Autocomplete System",
      "description": "Design a search autocomplete system for a search engine. Users may input a sentence, and the system should suggest at most 3 historical hot sentences that have the same prefix.",
      "example": "Input: sentences = ['i love you', 'island', 'iroman'], times = [5,3,2] -> Output: System that returns top 3 suggestions for any prefix"
    }
  ]
}
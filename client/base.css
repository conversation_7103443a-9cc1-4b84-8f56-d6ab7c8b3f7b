/* Import Japanese-inspired fonts */
@import url('https://fonts.googleapis.com/css2?family=Noto+Serif+JP:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Fira+Code:wght@300;400;500;600;700&display=swap');

/*
@tailwind base; adds base styles to all elements:
https://tailwindcss.com/docs/preflight
*/
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Zen Japanese Theme Colors */
  --color-base: #F7F3E9; /* Shiro - Traditional Japanese paper color */
  --color-ink: #1A1A1A; /* Sumi - Traditional Japanese ink */
  --color-accent: #D3A76D; /* Kuchiba - Fallen leaves color */
  --color-highlight: #A02C2C; /* Akane - Deep red */
  --color-calm: #7BA7BC; /* Asagi - Pale blue */
  --color-nature: #5A7247; /* Midori - Green */
  --color-shadow: rgba(0, 0, 0, 0.1);

  /* Dark Theme Colors */
  --color-dark-base: #1A1A1A;
  --color-dark-surface: #2A2A2A;
  --color-dark-accent: #8C6A93; /* Fuji - Wisteria purple */
  --color-dark-text: #E5E1D8;

  /* Transitions */
  --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

html,
body {
  height: 100%;
  width: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
  font-family: "Fira Code", "Fira Mono", monospace;
  font-size: 0.9rem;
  background-color: var(--color-base);
  color: var(--color-ink);
  transition: var(--transition-smooth);
}

/* Japanese-inspired styling */
h1, h2, h3, h4, h5, h6 {
  font-family: "Noto Serif JP", serif;
  font-weight: 500;
  letter-spacing: 0.02em;
}

button, select, input {
  border-radius: 2px;
  transition: var(--transition-smooth);
}

button {
  background-color: var(--color-ink);
  color: var(--color-base);
  border: none;
  padding: 0.5rem 1rem;
  cursor: pointer;
  font-family: "Noto Serif JP", serif;
  letter-spacing: 0.05em;
}

button:hover {
  background-color: var(--color-highlight);
  box-shadow: 0 4px 8px var(--color-shadow);
  transform: translateY(-1px);
}

select {
  background-color: var(--color-base);
  border: 1px solid var(--color-ink);
  padding: 0.4rem 0.8rem;
  font-family: "Noto Serif JP", serif;
}

/* Dark mode styling */
.dark-theme {
  --color-base: var(--color-dark-base);
  --color-highlight: var(--color-dark-accent);
  color: var(--color-dark-text);
}

.dark-theme button {
  background-color: var(--color-dark-accent);
  color: var(--color-dark-text);
}

.dark-theme button:hover {
  background-color: var(--color-accent);
}

.dark-theme select {
  background-color: var(--color-dark-surface);
  color: var(--color-dark-text);
  border-color: var(--color-dark-accent);
}

/* Editor fixes */
.react-simple-code-editor__textarea,
.react-simple-code-editor__highlight {
  white-space: pre !important;
  overflow: auto !important;
}

.react-simple-code-editor__textarea {
  min-height: 100% !important;
}

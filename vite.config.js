import { join, dirname, resolve } from "path";
import { fileURLToPath } from "url";
import react from "@vitejs/plugin-react";
import vike from 'vike/plugin'

const path = fileURLToPath(import.meta.url);

export default {
  root: join(dirname(path), "client"),
  plugins: [react(), vike()],
  server: {
    host: true,
    allowedHosts: [
      'localhost',
      '127.0.0.1',
      'samurai-interview.onrender.com',
      '.onrender.com',
      'www.samuraiinterview.com',
      'samuraiinterview.com'
    ]
  }
};

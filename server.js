import express from "express";
import fs from "fs";
import multer from "multer";
import { createServer as createViteServer } from "vite";
import "dotenv/config";

const app = express();
const port = process.env.PORT || 3000;
const openaiApiKey = process.env.OPENAI_API_KEY;
const deepgramApiKey = process.env.DEEPGRAM_API_KEY;

// Configure Vite middleware for React client
const vite = await createViteServer({
  server: { middlewareMode: true },
  appType: "custom",
});
app.use(vite.middlewares);

// Debug route to check if the server is running
app.get("/api/status", (req, res) => {
  res.json({
    status: "ok",
    environment: process.env.NODE_ENV,
    timestamp: new Date().toISOString(),
    openaiKeyExists: !!openaiApiKey,
    deepgramKeyExists: !!deepgramApiKey
  });
});

// API route for token generation
app.get("/token", async (req, res) => {
  let voice = "verse"
  try {
    if (!openaiApiKey) {
      console.error("OpenAI API key is missing");
      return res.status(500).json({ error: "OpenAI API key is missing" });
    }

    if (req.voice) {
      voice = req.voice
    }

    const response = await fetch(
      "https://api.openai.com/v1/realtime/sessions",
      {
        method: "POST",
        headers: {
          Authorization: `Bearer ${openaiApiKey}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          model: "gpt-4o-realtime-preview-2025-06-03",
          voice: voice,
          instructions:
            "You are a professional technical interviewer at a prestigious tech company. You maintain high standards while being fair and supportive. You are waiting for the candidate to join the call for their interview session.",
        }),
      }
    );

    if (!response.ok) {
      const errorText = await response.text();
      console.error("OpenAI API error:", response.status, errorText);
      return res.status(response.status).json({
        error: "OpenAI API error",
        status: response.status,
        details: errorText
      });
    }

    const data = await response.json();
    res.json(data);
  } catch (error) {
    console.error("Token generation error:", error);
    res.status(500).json({ error: "Failed to generate token", details: error.message });
  }
});

// Use multer to handle multipart/form-data uploads in memory
const upload = multer({ storage: multer.memoryStorage() });

// API route to transcribe and summarize the audio file
app.post("/transcribe", upload.single("audio"), async (req, res) => {
  try {
    if (!req.file)
      return res.status(400).json({ error: "No audio file uploaded" });

    // Transcribe using Deepgram API
    const transcription = await transcribeWithDeepgram(req.file);

    if (!transcription) {
      return res.status(500).json({ error: "Failed to transcribe audio with Deepgram" });
    }

    // Prepare messages for the Chat Completions API to generate a summary
    const messages = [
      {
        role: "system",
        content: `You are a world-class technical interviewer at a top tech company, known for your ability to accurately assess candidates and provide transformative feedback. Your evaluations have helped countless engineers reach their full potential. You maintain extremely high standards because you believe in helping candidates achieve excellence.

    You will receive a transcript of a technical interview, along with the code written by the candidate. Your task is to provide a detailed, actionable evaluation using the following dimensions. For each dimension, provide specific bullet points that justify the score:

    1. Correctness (25 points)
       • Score: [0-25]
       • Key Points:
         - [Specific example from code/conversation]
         - [Edge case handling]
         - [Logical flow]
         - [Error handling]

    2. Time/Space Complexity (15 points)
       • Score: [0-15]
       • Key Points:
         - [Current complexity analysis]
         - [Optimization opportunities]
         - [Data structure impact]
         - [Performance considerations]

    3. Code Structure (15 points)
       • Score: [0-15]
       • Key Points:
         - [Function organization]
         - [Code readability]
         - [Modularity]
         - [Maintainability]

    4. Use of Data Structures (10 points)
       • Score: [0-10]
       • Key Points:
         - [Data structure choices]
         - [Efficiency of operations]
         - [Alternative structures]
         - [Implementation details]

    5. Algorithm Design (15 points)
       • Score: [0-15]
       • Key Points:
         - [Algorithm approach]
         - [Efficiency]
         - [Problem-solving strategy]
         - [Optimization techniques]

    6. Debugging & Testing (10 points)
       • Score: [0-10]
       • Key Points:
         - [Test coverage]
         - [Edge case handling]
         - [Error scenarios]
         - [Validation approach]

    7. Communication (5 points)
       • Score: [0-5]
       • Key Points:
         - [Clarity of explanation]
         - [Technical terminology]
         - [Problem breakdown]
         - [Solution articulation]

    8. Iteration & Improvement (5 points)
       • Score: [0-5]
       • Key Points:
         - [Solution evolution]
         - [Feedback incorporation]
         - [Problem-solving approach]
         - [Adaptability]

    Return a JSON object with the following structure:
    {
      "score": number, // Total score out of 100 (must be the sum of all subscores)
      "score_justification": "string", // Overall summary of performance
      "feedback": {
        "correctness": {
          "score": number, // 0-25
          "points": ["point1", "point2", "point3", "point4"]
        },
        "time_space_complexity": {
          "score": number, // 0-15
          "points": ["point1", "point2", "point3", "point4"]
        },
        "code_structure": {
          "score": number, // 0-15
          "points": ["point1", "point2", "point3", "point4"]
        },
        "data_structures": {
          "score": number, // 0-10
          "points": ["point1", "point2", "point3", "point4"]
        },
        "algorithm_design": {
          "score": number, // 0-15
          "points": ["point1", "point2", "point3", "point4"]
        },
        "debugging_testing": {
          "score": number, // 0-10
          "points": ["point1", "point2", "point3", "point4"]
        },
        "communication": {
          "score": number, // 0-5
          "points": ["point1", "point2", "point3", "point4"]
        },
        "iteration_improvement": {
          "score": number, // 0-5
          "points": ["point1", "point2", "point3", "point4"]
        }
      },
      "sub_scores": {
        "correctness": number, // 0-25
        "time_space_complexity": number, // 0-15
        "code_structure": number, // 0-15
        "data_structures": number, // 0-10
        "algorithm_design": number, // 0-15
        "debugging_testing": number, // 0-10
        "communication": number, // 0-5
        "iteration_improvement": number // 0-5
      }
    }

    IMPORTANT: You MUST follow this exact JSON structure. Do not add additional fields or change the structure.
    The response MUST be valid JSON that can be parsed with JSON.parse().

    IMPORTANT: Since I'm using the response_format: { type: "json_object" } parameter, your response will be
    automatically parsed as JSON. Make sure your response is a valid JSON object with the structure above.

    Important Scoring Rules:
    1. The total score (score) MUST be the sum of all subscores
    2. Each subscore must be within its specified range
    3. The maximum possible total score is 100 (25 + 15 + 15 + 10 + 15 + 10 + 5 + 5)
    4. Each category's score in the feedback object must match its corresponding subscore
    5. Double-check that the sum of subscores equals the total score before returning

    Remember:
    - Each bullet point should be specific and actionable
    - Reference actual code or conversation examples
    - Provide concrete suggestions for improvement
    - Balance strengths and areas for growth
    - Use technical terminology appropriately
    - Keep feedback constructive and professional`,
      },
      {
        role: "user",
        content: `Here is the full transcript and the code written by the candidate.

    ---TRANSCRIPT---
    ${transcription}

    ---CANDIDATE'S CODE---
    ${req.body.code}
    `,
      },
    ];

    // Call the Chat Completions endpoint
    // Add subscores, suggestions, lots of stuff!!
    const chatResponse = await fetch(
      "https://api.openai.com/v1/chat/completions",
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${openaiApiKey}`,
        },
        body: JSON.stringify({
          model: "gpt-4o",  // Using gpt-4o which supports structured outputs
          messages: messages,
          temperature: 0,
          top_p: 0.05,
          max_tokens: 8096,
          response_format: { type: "json_object" }
        }),
      }
    );

    if (!chatResponse.ok) {
      const errorText = await chatResponse.text();
      console.error("Chat Completion error:", errorText);

      // Create a fallback response with error information
      // Note: We're using a string for content to ensure it's properly parsed by the client
      const fallbackResponse = {
        choices: [{
          message: {
            content: JSON.stringify({
              score: 0,
              score_justification: `OpenAI API Error: ${errorText}`,
              feedback: "The system encountered an error while processing your interview. Please try again.",
              sub_scores: {
                correctness: 0,
                time_space_complexity: 0,
                code_structure: 0,
                data_structures: 0,
                algorithm_design: 0,
                debugging_testing: 0,
                communication: 0,
                iteration_improvement: 0
              }
            })
          }
        }]
      };

      // Log the fallback response for debugging
      console.log("Sending fallback response:", JSON.stringify(fallbackResponse, null, 2));

      // Return the fallback response instead of an error
      return res.json(fallbackResponse);
    }

    const chatData = await chatResponse.json();
    console.log('OpenAI Response:', JSON.stringify(chatData, null, 2));

    // Handle the response content
    let content = chatData.choices?.[0]?.message?.content;
    console.log('Raw content before processing:', typeof content, content);

    // When using response_format: { type: "json_object" }, the content should already be an object
    // But we'll handle both cases for robustness
    if (typeof content === "string") {
      try {
        // Try to parse it as JSON
        content = JSON.parse(content);
        console.log('Successfully parsed string content to JSON object');
      } catch (e) {
        console.error("Failed to parse JSON content:", e);
        // Instead of failing, create a basic structure with the error message
        content = {
          score: 0,
          score_justification: `Error parsing response: ${e.message}`,
          feedback: "The system encountered an error while processing your interview. Please try again.",
          sub_scores: {}
        };
      }
    }

    // Validate that we have an object
    if (!content || typeof content !== "object") {
      console.error("Invalid content type after processing:", typeof content);
      content = {
        score: 0,
        score_justification: "Invalid response format received",
        feedback: "The system encountered an error while processing your interview. Please try again.",
        sub_scores: {}
      };
    }

    // Ensure the content has the expected structure
    if (!content.score) content.score = 0;

    // Ensure score_justification is a string
    if (!content.score_justification) {
      content.score_justification = "No assessment available";
    } else if (typeof content.score_justification !== 'string') {
      // Convert to string if it's not already
      content.score_justification = JSON.stringify(content.score_justification);
    }

    // Ensure feedback is properly formatted
    if (!content.feedback) {
      content.feedback = "No detailed feedback available";
    } else if (typeof content.feedback !== 'string' && typeof content.feedback !== 'object') {
      // Convert to string if it's not a string or object
      content.feedback = JSON.stringify(content.feedback);
    }

    // Make sure all expected categories have values in sub_scores
    if (!content.sub_scores) content.sub_scores = {};

    // Ensure all categories exist in sub_scores
    ['correctness', 'time_space_complexity', 'code_structure', 'data_structures',
     'algorithm_design', 'debugging_testing', 'communication', 'iteration_improvement'].forEach(category => {
      if (content.sub_scores[category] === undefined) {
        // Try to get score from feedback object
        if (content.feedback &&
            typeof content.feedback === 'object' &&
            content.feedback[category] &&
            typeof content.feedback[category] === 'object' &&
            'score' in content.feedback[category]) {
          content.sub_scores[category] = content.feedback[category].score;
        } else {
          // Default to 0 if not found
          content.sub_scores[category] = 0;
        }
      }
    });

    // Update the content in the response
    chatData.choices[0].message.content = content;
    console.log('Final processed content:', content);

    res.json(chatData);

  } catch (error) {
    console.error("Transcription/Summary processing error:", error);
    res.status(500).json({ error: "Processing failed" });
  }
});

// Function to transcribe audio using Deepgram
async function transcribeWithDeepgram(file) {
  try {
    const response = await fetch("https://api.deepgram.com/v1/listen?model=nova-2&smart_format=true", {
      method: "POST",
      headers: {
        Authorization: `Token ${deepgramApiKey}`,
        "Content-Type": file.mimetype,
      },
      body: file.buffer,
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error("Deepgram transcription error:", errorText);
      return null;
    }

    const data = await response.json();
    // Extract the transcript text from Deepgram's response
    return data.results?.channels[0]?.alternatives[0]?.transcript || "";
  } catch (error) {
    console.error("Error transcribing with Deepgram:", error);
    return null;
  }
}

// Import path for resolving file paths
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Serve static assets in production
if (process.env.NODE_ENV === 'production') {
  // First try to serve from client directory
  try {
    const clientPath = path.resolve(__dirname, 'client');
    if (fs.existsSync(clientPath)) {
      app.use(express.static(clientPath));
    }
  } catch (err) {
    console.error('Error setting up static file serving from client directory:', err);
  }

  // Then try to serve from dist/client if it exists
  try {
    const clientDistPath = path.resolve(__dirname, 'dist/client');
    if (fs.existsSync(clientDistPath)) {
      app.use(express.static(clientDistPath));
    }
  } catch (err) {
    console.error('Error setting up static file serving from dist/client:', err);
  }

  // Always serve from public directory as fallback
  try {
    const publicPath = path.resolve(__dirname, 'public');
    if (fs.existsSync(publicPath)) {
      app.use(express.static(publicPath));
    }
  } catch (err) {
    console.error('Error setting up static file serving from public directory:', err);
  }
}

// Add a specific route for the application
app.get('/app', async (req, res, next) => {
  try {
    if (process.env.NODE_ENV === 'production') {
      // In production, serve the client app directly
      const clientIndexPath = path.resolve(__dirname, 'client/index.html');
      if (fs.existsSync(clientIndexPath)) {
        const template = fs.readFileSync(clientIndexPath, 'utf-8');
        res.status(200).set({ "Content-Type": "text/html" }).end(template);
      } else {
        // Fallback to a simple HTML response if client/index.html doesn't exist
        console.log('client/index.html not found, sending simple HTML response');
        res.send(`
          <!DOCTYPE html>
          <html>
            <head>
              <title>Samurai Interview</title>
              <meta charset="utf-8">
              <meta name="viewport" content="width=device-width, initial-scale=1.0">
              <style>
                body {
                  font-family: 'Noto Serif JP', serif;
                  background-color: #F7F3E9;
                  color: #1A1A1A;
                  display: flex;
                  flex-direction: column;
                  align-items: center;
                  justify-content: center;
                  height: 100vh;
                  margin: 0;
                  text-align: center;
                  padding: 20px;
                }
                h1 { font-size: 2rem; margin-bottom: 1rem; }
              </style>
            </head>
            <body>
              <h1>Samurai Interview</h1>
              <p>Loading application...</p>
              <script>
                // Reload the page after a short delay
                setTimeout(() => window.location.reload(), 3000);
              </script>
            </body>
          </html>
        `);
      }
    } else {
      // In development, use Vite's dev server
      const template = await vite.transformIndexHtml(
        req.originalUrl,
        fs.readFileSync(path.resolve(__dirname, "client/index.html"), "utf-8")
      );
      const { render } = await vite.ssrLoadModule("./client/entry-server.jsx");
      const appHtml = await render(req.originalUrl);
      const html = template.replace(`<!--ssr-outlet-->`, appHtml?.html);
      res.status(200).set({ "Content-Type": "text/html" }).end(html);
    }
  } catch (e) {
    console.error('Error serving app route:', e);
    if (vite) vite.ssrFixStacktrace(e);
    next(e);
  }
});

// Add a root route to redirect directly to the app
app.get('/', (_, res) => {
  // In production, serve the client app directly
  if (process.env.NODE_ENV === 'production') {
    const clientIndexPath = path.resolve(__dirname, 'client/index.html');
    if (fs.existsSync(clientIndexPath)) {
      const template = fs.readFileSync(clientIndexPath, 'utf-8');
      res.status(200).set({ "Content-Type": "text/html" }).end(template);
    } else {
      // If client/index.html doesn't exist, redirect to /app as fallback
      res.redirect('/app');
    }
  } else {
    // In development, use Vite's dev server
    res.redirect('/app');
  }
});

// Render the React client using Vite SSR for all other routes
app.use("*", async (req, res, next) => {
  const url = req.originalUrl;

  try {
    if (process.env.NODE_ENV === 'production') {
      try {
        // Check if this is an API request
        if (url.startsWith('/api/') || url === '/token' || url === '/transcribe') {
          console.log(`API route not found: ${url}`);
          return res.status(404).json({ error: 'API endpoint not found' });
        }

        // Handle static assets
        if (url.startsWith('/assets/') || url.includes('.')) {
          // Let Express static middleware handle it
          next();
          return;
        }

        // For all other routes, serve the client app directly
        const clientIndexPath = path.resolve(__dirname, 'client/index.html');
        if (fs.existsSync(clientIndexPath)) {
          console.log(`Serving client/index.html for route: ${url}`);
          const template = fs.readFileSync(clientIndexPath, 'utf-8');
          res.status(200).set({ "Content-Type": "text/html" }).end(template);
        } else {
          // Redirect to /app as fallback
          console.log(`client/index.html not found, redirecting to /app`);
          res.redirect('/app');
        }
      } catch (err) {
        console.error('Error serving index.html:', err);
        res.status(500).send('Server Error');
      }
    } else {
      // In development, use Vite's dev server
      const template = await vite.transformIndexHtml(
        url,
        fs.readFileSync(path.resolve(__dirname, "client/index.html"), "utf-8")
      );
      const { render } = await vite.ssrLoadModule("./client/entry-server.jsx");
      const appHtml = await render(url);
      const html = template.replace(`<!--ssr-outlet-->`, appHtml?.html);
      res.status(200).set({ "Content-Type": "text/html" }).end(html);
    }
  } catch (e) {
    console.error('Server error:', e);
    if (vite) vite.ssrFixStacktrace(e);
    next(e);
  }
});

// Add this near your other environment variables
const CREDIT_PACKAGES = [
  { id: process.env.STRIPE_PRICE_ID_1, name: 'Novice Pack', credits: 3, price: '$15' },
  { id: process.env.STRIPE_PRICE_ID_2, name: 'Warrior Pack', credits: 10, price: '$45' },
  { id: process.env.STRIPE_PRICE_ID_3, name: 'Mastery Pack', credits: 25, price: '$100' }
];

// Add this with your other API routes
app.get("/api/credit-packages", (_, res) => {
  // Only send the public information, not the price IDs
  const publicPackages = CREDIT_PACKAGES.map(({ id, ...rest }) => rest);
  res.json(publicPackages);
});

app.listen(port, () => {
  console.log(`Express server running on *:${port}`);
});
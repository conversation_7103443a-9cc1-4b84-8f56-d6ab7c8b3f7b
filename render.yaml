services:
  - type: web
    name: samurai-interview
    env: node
    buildCommand: npm install && npm run build:full
    startCommand: npm start
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: 10000
      - key: OPENAI_API_KEY
        sync: false
      - key: DEEPGRAM_API_KEY
        sync: false
      - key: VAPI_API_KEY
        sync: false
      - key: VAPI_ENABLED
        value: "true"

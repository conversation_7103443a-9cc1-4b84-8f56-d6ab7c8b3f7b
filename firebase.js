// Import the functions you need from the SDKs you need
import { initializeApp, getApps, getApp } from "firebase/app";
import { getAuth, setPersistence, browserLocalPersistence } from 'firebase/auth';
import { getFirestore } from "firebase/firestore"
// TODO: Add SDKs for Firebase products that you want to use
// https://firebase.google.com/docs/web/setup#available-libraries

// Your web app's Firebase configuration
// For Firebase JS SDK v7.20.0 and later, measurementId is optional

// THIS IS FINE TO EXPOSE TO THE PUBLIC, but the OPEN AI key and Stripe Keys must be hidden
const firebaseConfig = {
  apiKey: "AIzaSyARDdZzPLQmxDHVx2sB2qkLj9EjWWXEKUk",
  authDomain: "interview-gym-beta.firebaseapp.com",
  projectId: "interview-gym-beta",
  storageBucket: "interview-gym-beta.firebasestorage.app",
  messagingSenderId: "1004047470186",
  appId: "1:1004047470186:web:e421942c0fa675a63d8413",
  measurementId: "G-943G55B1SY",
};

// Initialize Firebase
const app = !getApps().length ? initializeApp(firebaseConfig) : getApp()
export const auth = getAuth(app)
export const db   = getFirestore(app);

// Set persistence to LOCAL to keep user logged in
// This ensures the user stays logged in even if they close the browser
setPersistence(auth, browserLocalPersistence)
  .catch((error) => {
    console.error("Firebase persistence error:", error);
  });

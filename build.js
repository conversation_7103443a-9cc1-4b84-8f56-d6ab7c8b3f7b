import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Create public directory if it doesn't exist
const publicDir = path.resolve(__dirname, 'public');
if (!fs.existsSync(publicDir)) {
  fs.mkdirSync(publicDir, { recursive: true });
}

// Create a simple index.html file in the public directory
const indexHtml = `<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <title>Samurai Interview</title>
    <meta name="description" content="Samurai Interview" />
    <meta name="author" content="Samurai Interview" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <style>
      body {
        font-family: 'Noto Serif JP', serif;
        background-color: #F7F3E9;
        color: #1A1A1A;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100vh;
        margin: 0;
        padding: 20px;
        text-align: center;
      }
      h1 {
        font-size: 2.5rem;
        margin-bottom: 1rem;
      }
      p {
        font-size: 1.2rem;
        max-width: 600px;
        line-height: 1.6;
      }
      .loading {
        margin-top: 2rem;
        font-size: 1rem;
        color: #A02C2C;
      }
    </style>
  </head>
  <body>
    <h1>Samurai Interview</h1>
    <p>Welcome to Samurai Interview, your technical interview practice platform.</p>
    <div class="loading">Loading application...</div>
    <p style="margin-top: 20px;">
      <a href="/app" style="display: inline-block; padding: 10px 20px; background-color: #A02C2C; color: white; text-decoration: none; border-radius: 4px; font-family: sans-serif;">
        Launch Application
      </a>
    </p>

    <script>
      // Add a small delay before checking API status
      setTimeout(function() {
        // Try to load the API routes first to check if the server is responding
        fetch('/api/status')
          .then(response => response.json())
          .then(data => {
            console.log('API status:', data);
            if (data.status === 'ok') {
              document.querySelector('.loading').innerHTML = 'Server is online. Click the button above to launch the application.';
            } else {
              document.querySelector('.loading').innerHTML = 'Server is online but may have configuration issues. Try launching the application anyway.';
            }
          })
          .catch(error => {
            console.error('Error checking API status:', error);
            document.querySelector('.loading').innerHTML = 'Could not connect to API. The server may be starting up. Try launching the application in a moment.';
          });
      }, 1000);
    </script>
  </body>
</html>`;

fs.writeFileSync(path.resolve(publicDir, 'index.html'), indexHtml);

// Try to copy client/index.html to public/app.html
try {
  const clientIndexPath = path.resolve(__dirname, 'client/index.html');
  if (fs.existsSync(clientIndexPath)) {
    const clientIndexContent = fs.readFileSync(clientIndexPath, 'utf-8');
    fs.writeFileSync(path.resolve(publicDir, 'app.html'), clientIndexContent);
  } else {
    console.log('client/index.html not found, skipping copy');
  }
} catch (err) {
  console.error('Error copying client/index.html:', err);
}

// Create a simple app.html file if client/index.html doesn't exist
if (!fs.existsSync(path.resolve(publicDir, 'app.html'))) {
  console.log('Creating fallback public/app.html');
  const appHtml = `<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <title>Samurai Interview App</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <style>
      body {
        font-family: 'Noto Serif JP', serif;
        background-color: #F7F3E9;
        color: #1A1A1A;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100vh;
        margin: 0;
        padding: 20px;
        text-align: center;
      }
    </style>
  </head>
  <body>
    <h1>Samurai Interview</h1>
    <p>Application is loading...</p>
    <p>If you continue to see this message, please check the server logs for errors.</p>
    <p><a href="/">Return to home</a></p>
  </body>
</html>`;
  fs.writeFileSync(path.resolve(publicDir, 'app.html'), appHtml);
  console.log('Created fallback public/app.html');
}

console.log('Build script completed successfully');
